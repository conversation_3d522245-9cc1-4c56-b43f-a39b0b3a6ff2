import { getPage, closeBrowser } from './config/puppeterInstance.js';
import fs from 'fs/promises';

(async () => {
  const page = await getPage();
  await page.goto('https://www.smartprix.com/', { waitUntil: 'domcontentloaded' });

  await page.waitForSelector('input[name="q"]', { timeout: 10000 });

  const searchTerm = 'realme 3 pro';
  await page.type('input[name="q"]', searchTerm);

  await page.keyboard.press('Enter');

  await page.waitForSelector('.pg-prf-head', { timeout: 20000 });

  const products = await page.evaluate(() => {
    return Array.from(document.querySelectorAll('a.name.clamp-2')).map(a => ({
      name: a.innerText.trim(),
      url: a.href.startsWith('http') ? a.href : `https://www.smartprix.com${a.getAttribute('href')}`,
    }));
  });

  await fs.writeFile('search-batch-1.json', JSON.stringify(products, null, 2), 'utf8');
  console.log(`Wrote ${products.length} products from first search batch!`);

  await closeBrowser();
})();
