import random

def generate_review_metrics(target_review_value):
    metrics = [round(random.uniform(max(1.0, target_review_value - 1.0), min(5.0, target_review_value + 1.0)), 1) for _ in range(5)]
    sum_of_metrics = sum(metrics)
    metrics_6 = round(6.0 * target_review_value - sum_of_metrics, 1)
    if metrics_6 < 1.0 or metrics_6 > 5.0:
        adjustment = round((metrics_6 - 3.0) / 5.0, 1)
        metrics = [round(min(max(1.0, num + adjustment), 5.0), 1) for num in metrics]
        metrics_6 = round(6.0 * target_review_value - sum(metrics), 1)
    metrics.append(metrics_6)
    return metrics
