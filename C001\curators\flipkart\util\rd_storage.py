from google.cloud import storage
from dotenv import load_dotenv  # type: ignore
import os
import json

load_dotenv()

project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
bucket_name = os.getenv('RD_DF_RAW_DATA_BUCKET_NAME')
input_dir = os.getenv('INPUT_DIR')

storage_client = storage.Client(project=project_id)

def list_input_file_paths(input_dir):
    if not input_dir:
        raise ValueError("Error: INPUT_DIR not set.")
    if not input_dir.endswith('/'):
        input_dir += '/'
    
    blob_list = storage_client.list_blobs(bucket_name, prefix=input_dir)
    input_file_list = [blob.name for blob in blob_list]
    return input_file_list

def read_input_file_content(input_file_path):
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(input_file_path)
    
    if not blob.exists():
        raise FileNotFoundError(f"Not found: '{input_file_path}' in '{bucket_name}'.")
    
    input_file_data = blob.download_as_text()
    return json.loads(input_file_data)
