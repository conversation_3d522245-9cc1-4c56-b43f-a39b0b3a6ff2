import mysql.connector # type: ignore
from mysql.connector import pooling # type: ignore
import os
from dotenv import load_dotenv # type: ignore

load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '.env'))

db_config = {
    "host": os.getenv('RD_DB_STG_HOST'),
    "user": os.getenv('RD_DB_STG_USER'),
    "password": os.getenv('RD_DB_STG_PASSWORD'),
    "database": os.getenv('RD_DB_STG_DATABASE')
}

def initiate_connection_pool(pool_name="connection_pool", pool_size=5, **db_config):
    try:
        connection_pool = mysql.connector.pooling.MySQLConnectionPool(
            pool_name=pool_name,
            pool_size=pool_size,
            pool_reset_session=True,
            **db_config
        )
        return connection_pool
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        return None

connection_pool = initiate_connection_pool(pool_name="connection_pool", pool_size=5, **db_config)

def get_connection_from_pool(pool):
    try:
        connection = pool.get_connection()
        return connection
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        return None
