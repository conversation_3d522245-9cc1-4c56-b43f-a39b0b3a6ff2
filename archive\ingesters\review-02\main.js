import axios from 'axios';
import { getMainProductPageContent, getNextReviewPageLink } from './utils/getRawContent.js';
import { extractReviewDetails, extractReviewRatings, extractReviewValue, getDetailsReviewPage } from './utils/extractAttributes.js';

const axiosClient = axios.create({
    timeout: process.env.AXIOS_TIMEOUT,
});

export const startIngester = async (PRODUCT_URL, phoneName) => {
    try {
        const mainProductPageContent = await getMainProductPageContent(axiosClient, PRODUCT_URL);
        const detailReviewPageContent = await getDetailsReviewPage(axiosClient, mainProductPageContent);
        const reviewRatings = extractReviewRatings(detailReviewPageContent);
        const reviewValue = extractReviewValue(detailReviewPageContent);
        const reviews = await getAttributes(detailReviewPageContent, [], 1);

        return {
            reviewValue,
            reviews,
            reviewRatings
        }
    } catch (error) {
        console.log("Main fnc : ", error);
    }
}

const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

const getAttributes = async (detailReviewPageContent, reviews, count) => {
    try {
        if (count === 9) {
            console.log("Waiting 5 sec...");
            await delay(5000);
            count = 1;
        }
        const extractedReviews = await extractReviewDetails(detailReviewPageContent);
        reviews.push(extractedReviews);
        const nextPageLink = await getNextReviewPageLink(detailReviewPageContent);

        if (nextPageLink) {
            const newDetailsPageContent = await getDetailsReviewPage(axiosClient, nextPageLink, true);
            return getAttributes(newDetailsPageContent, reviews, ++count);
        } else {
            return reviews;
        }
    } catch (error) {
        console.log(error);
        return reviews;
    }
}

