import aiohttp
from aiohttp import FormData
from bs4 import BeautifulSoup
import os
import json
import aiofiles
from aiofiles.os import path as aiopath
import base64

def get_home_page_url(page):
    return f'https://www.91mobiles.com/template/category_finder/finder_ajax.php?ord=0.297044338904773&excludeId=&hash=&search=&hidFrmSubFlag=1&page={page}&category=mobile&unique_sort=&gaCategory=PhoneFinder-filter&requestType=2&showPagination=1&listType=list&listType_v3=list&listType_v1=list&listType_v2=list&listType_v4=list&listType_v5=list&listType_v6=list&page_type=finder&finderRuleUrl=&selMobSort=views&hdnCategory=mobile&user_search=&url_feat_rule=&buygaCat=finder-mob&isCustomAuto=0&buygaCat=finder-mob&isCustomAuto=0&hdndprice=25001-25000%2C30001-100000&hdndprice1=15001-25000&amount=0%3B65000&sCatName=mobile&price_range_apply=0&request_uri=/phonefinder.php'

async def scrape_home_page_content(link, client, idx):
    try:
        async with client.get(link, headers={'User-Agent': 'Mozilla/5.0'}) as response:
            data = await response.json()
            return data['response']
    except Exception as e:
        print(f'ERROR :: while scraping detail page links {e}')
        return False

async def get_details_page_links(data):
    try:
        soup = BeautifulSoup(data, 'html.parser')
        links = ['https://www.91mobiles.com' + a['href'] for a in soup.select('a.hover_blue_link.name.gaclick')]
        return links
    except Exception as e:
        print(e)

async def extract_product_attributes(link, client):
    try:
        async with client.get(link, headers={'User-Agent': 'Mozilla/5.0'}) as response:
            data = await response.text()
            soup = BeautifulSoup(data, 'html.parser')

            phone_name_element = soup.select_one('h1.h1_pro_head')
            phone_name = phone_name_element.text.strip() if phone_name_element else ''

            rating_text_element = soup.select_one('.ratpt')
            rating_text = rating_text_element.text.strip() if rating_text_element else '0.0'
            rating = float(rating_text.split(' ')[0])

            price_element_1 = soup.select_one('.store_prc')
            price_text = price_element_1.text.strip() if price_element_1 else ''

            price_elements_2 = soup.select('.big_prc')
            price_element_2 = price_elements_2[1] if len(price_elements_2) > 1 else None
            data_price = price_element_2['data-price'] if price_element_2 and 'data-price' in price_element_2.attrs else price_text

            image_elements = soup.select('img.alb_img.lazy[alt="Design"]')
            image_data_srcs = [img['data-src'] for img in image_elements if 'data-src' in img.attrs]

            script_elements = soup.select('script[type="text/javascript"]')
            script_content = script_elements[1].string if len(script_elements) > 1 else ''
            brand, model = '', ''
            if script_content:
                try:
                    data_layer = json.loads(script_content.split('dataLayer = ')[1].split(';')[0])
                    product_info = next((item for item in data_layer if 'ProductModelName' in item and 'Productbrand' in item), None)
                    if product_info:
                        brand = product_info['Productbrand']
                        model = product_info['ProductModelName']
                except (IndexError, json.JSONDecodeError):
                    pass

            specs = []
            for spec_box in soup.select('.spec_box'):
                spec_items = [
                    {
                        'title': tr.select_one('.spec_ttle').text.strip() if tr.select_one('.spec_ttle') else '',
                        'description': tr.select_one('.spec_des').text.strip() if tr.select_one('.spec_des') else ''
                    }
                    for tr in spec_box.select('.spec_table tr')
                ]
                specs.append(spec_items)

            product_image_element = soup.select_one('img.overview_lrg_pic_img')
            product_image = product_image_element['src'] if product_image_element else ''

            images_data_srcs = [img['data-src'] for img in soup.select('img.alb_img.lazy-loaded')]

            downloaded_product_images = await upload_to_rd_storage(image_data_srcs, phone_name)
            print(downloaded_product_images, brand)

            return {
                'brand': brand,
                'downloaded_product_images': downloaded_product_images,
                'model': model,
                'data_price': data_price,
                'phone_name': phone_name,
                'product_image': product_image,
                'rating': rating,
                'ratings': [r.contents[0].strip() for r in soup.select('.specMtrTxt.clr')],
                'specs': specs
            }
    except Exception as e:
        print("Error in extract attributes : ", e)

async def export_raw_data(file_path, data):
    raw_data = json.dumps(data, indent=2)
    async with aiofiles.open(file_path, 'w') as f:
        await f.write(raw_data)
    print(f"Data export successfully: {file_path}")

async def upload_to_rd_storage(file_name, batch_count):
    try:
        media_exchange_url = os.getenv('SRVC_MEDIAEXCHANGE_URL')
        form = FormData()
        form.add_field('fileName', file_name)
        form.add_field('bucket', os.getenv('RD_RAW_DATA_BUCKET'))
        form.add_field('folder', os.getenv('RD_BUCKET_FOLDER_PATH'))
        async with aiofiles.open(file_name, 'rb') as f:
            form.add_field('file', f.read(), filename=file_name)
        async with aiohttp.ClientSession() as client:
            async with client.post(f"{media_exchange_url}/upload-asset1", data=form) as response:
                print(f"Raw data file uploaded to storage successfully: Batch No. {batch_count}")
    except Exception as e:
        print(f"Error in uploading raw file to bucket: {e}")

async def append_to_file(file_name, content):
    file_path = os.path.abspath(file_name)
    exists = await aiopath.exists(file_path)
    mode = 'a' if exists else 'w'
    async with aiofiles.open(file_path, mode) as f:
        await f.write(content + ',')

def sanitize_image_url(url):
    return url.split('?')[0]

def replace_spaces_with_hyphens(input_string):
    return input_string.replace(' ', '-')

async def download_image_to_base64(url):
    try:
        async with aiohttp.ClientSession() as client:
            async with client.get(url) as response:
                data = await response.read()
                return base64.b64encode(data).decode('utf-8')
    except Exception as e:
        print(f"Error : image to base64 {e}")

async def upload_to_rd_storage(images, phone_name):
    try:
        product_images = []
        async with aiohttp.ClientSession() as client:
            for count, image_url in enumerate(images, 1):
                sanitized_url = sanitize_image_url(image_url)
                image_base64_data = await download_image_to_base64(sanitized_url)
                file_name = f"{replace_spaces_with_hyphens(phone_name)}_{count}.png"
                image_buffer = base64.b64decode(image_base64_data)
                form = FormData()
                form.add_field('fileName', file_name)
                form.add_field('bucket', os.getenv('RD_MEDIA_BUCKET_NAME'))
                form.add_field('folder', os.getenv('RD_MEDIA_BUCKET_FOLDER_PATH'))
                form.add_field('file', image_buffer, filename=file_name, content_type='image/png')
                async with client.post(f"{os.getenv('SRVC_MEDIAEXCHANGE_URL')}/upload-asset", data=form) as response:
                    data = await response.json()
                    product_images.append(data['urls'][0])
        return product_images
    except Exception as e:
        print(f"Error : in uploading image to bucket : {e}")

