from google.cloud import storage
import json
import os
from utils.curation_utils import extract_review_details
from rd_df_Common.data_pump import insert_reviews

rd_bucket_name = 'rd-bckt-df-data-harvester'

def get_storage_client():
    return storage.Client()

def read_files_from_rd_bucket(folder_name, client):
    print(f"Listing files in bucket '{rd_bucket_name}' with prefix '{folder_name}'...")
    review_data_files = client.list_blobs(rd_bucket_name, prefix=folder_name)
    review_files = [blob.name for blob in review_data_files]
    print(f"Found {len(review_files)} files.")
    return review_files

def read_review_data_files(file_name, client):
    print(f"Reading content from file '{file_name}'...")
    rd_bucket = client.bucket(rd_bucket_name)
    raw_content = rd_bucket.blob(file_name)
    raw_data = raw_content.download_as_text()
    print(f"Content read from file '{file_name}'.")
    return raw_data

def start_curator():
    client = get_storage_client()
    try:    
        raw_review_files = read_files_from_rd_bucket('C001/review/02', client)
        
        for file_name in raw_review_files:
            file_content = read_review_data_files(file_name, client)
            try:
                print(f"File Content of '{file_name}'")
                json_content = json.loads(file_content)
                review_info = extract_review_details(json_content)    
                insert_reviews(review_info)
            except json.JSONDecodeError as e:
                print(f"Error decoding JSON for file '{file_name}': {e}")
    
    except Exception as e:
        print(f"An error occurred: {e}")

start_curator()
