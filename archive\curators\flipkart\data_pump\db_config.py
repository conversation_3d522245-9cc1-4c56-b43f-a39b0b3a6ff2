import mysql.connector  # type: ignore
from mysql.connector import pooling  # type: ignore
from google.cloud import secretmanager  # type: ignore
import tempfile
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def access_secret(secret_id: str, version_id: str = "latest") -> str:
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/reviewdale-datafabric/secrets/{secret_id}/versions/{version_id}"
    response = client.access_secret_version(request={"name": name})
    return response.payload.data.decode("UTF-8")

def write_temp_file(contents: str, suffix: str = ".pem") -> str:
    temp = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
    temp.write(contents.encode("utf-8"))
    temp.flush()
    return temp.name

def initiate_connection_pool(pool_name="connection_pool", pool_size=5):
    try:
        db_config = {
            "host": access_secret("rd_db_stg_host"),
            "user": access_secret("rd_db_stg_user"),
            "password": access_secret("rd_db_stg_password"),
            "database": access_secret("rd_db_stg_database"),
            "ssl_ca": write_temp_file(access_secret("rd_db_stg_ssl_ca")),
            "ssl_cert": write_temp_file(access_secret("rd_db_stg_ssl_cert")),
            "ssl_key": write_temp_file(access_secret("rd_db_stg_ssl_key")),
        }

        connection_pool = mysql.connector.pooling.MySQLConnectionPool(
            pool_name=pool_name,
            pool_size=pool_size,
            pool_reset_session=True,
            **db_config
        )
        return connection_pool
    except Exception as err:
        logger.info(f"Failed: Initiating connection pool: {err}")
        return None

connection_pool = initiate_connection_pool()

def get_connection_from_pool(pool):
    try:
        if pool is None:
            return None
        connection = pool.get_connection()
        return connection
    except mysql.connector.Error as err:
        logger.info(f"Failed: Getting connection from pool: {err}")
        return None
