const fs = require('fs').promises;

function loadRawData(filePath) {
    return fs.readFile(filePath, 'utf8')
        .then(data => {
            const rawData = JSON.parse(data);
            return rawData;
        })
        .catch(err => {
            console.error(`Error loading JSON file ${filePath}:`, err);
            throw err;
        });
}

const filePath = process.env.FILENAME;

loadRawData(filePath)
    .then(data => {
    })
    .catch(err => {
        console.error('Error:', err);
    });
