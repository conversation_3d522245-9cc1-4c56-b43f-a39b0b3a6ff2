import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
sqlite3.verbose();

async function initializeCacheDatabase() {
    const db = await open({
        filename: './rd-df-C001.db3',
        driver: sqlite3.Database,
    });

    await db.exec(`CREATE TABLE IF NOT EXISTS reviews_ingestion_c001 (
                  product_id TEXT PRIMARY KEY,
                  product_name TEXT NOT NULL,
                  date Date,
                  brand TEXT,
                  model TEXT,
                  series TEXT,
                  variant TEXT,
                  checklist_01 INTEGER,
                  checklist_02 INTEGER
                )`);
    console.log('Table "reviews_ingestion_c001" created or already exists.');
    return db;
}

const dbPromise = initializeCacheDatabase();
export default dbPromise;
