from sqlalchemy import create_engine, Column, Integer, String, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import sqlalchemy as sa

Base = declarative_base()

class Product(Base):
    __tablename__ = "mstr_product"

    product_id = Column(String, primary_key=True)
    brand_name = Column(String)
    model_name = Column(String)
    series_name = Column(String)
    variant_name = Column(String)

class Review(Base):
    __tablename__ = "mstr_review"

    review_id = Column(Integer, primary_key=True)
    product_id = Column(String)
    review_value = Column(Integer)
    user_text = Column(String)

class Price(Base):
    __tablename__ = "mstr_product_price"
    price_id = Column(Integer, primary_key=True)
    product_id = Column(String)
    product_lowest_price = Column(Float)

class DataPump:
    def __init__(self, rdDB_server_url):
        self.engine = create_engine(rdDB_server_url)
        self.metadata = Base.metadata
        self.metadata.create_all(self.engine)
        self.session = sessionmaker(bind=self.engine)()

    def read_from_rd_db(self, information_type, **kwargs):
        rdDB_table_columns = [column for column in information_type.__table__.columns]
        pull_query = sa.select(*rdDB_table_columns).select_from(information_type.__table__)  # Corrected line
        if kwargs:
            filters = []
            for key, value in kwargs.items():
                filters.append(getattr(information_type.__table__.c, key) == value)
            pull_query = pull_query.where(*filters)
        results = self.session.execute(pull_query).fetchall()
        return [row._asdict() for row in results]

    def get_existing_products(self, **kwargs):
        return self.read_from_rd_db(Product, **kwargs)

    def add_new_products(self, new_products_list):
        for product in new_products_list:
            stmt = sa.insert(Product).values(**product.__dict)
