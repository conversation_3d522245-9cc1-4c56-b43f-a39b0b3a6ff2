import { getPage, closeBrowser } from '../config/puppeterInstance.js';
import { PRODUCTS } from '../settings.js'
import { fetchProductInfo } from './cheerio.js';
import { randomScroll, randomMouseMoves, randInt, matchProductName, uploadProductInfo } from './util.js';

async function searchProduct(productName) {
    const page = await getPage();
    try {
        await page.goto('https://www.smartprix.com/', { waitUntil: 'domcontentloaded', timeout: 70000 });
        await page.waitForSelector('input[name="q"]', { timeout: 70000 });

        await randomMouseMoves(page, randInt(2, 5));
        await randomScroll(page);

        const searchBox = await page.$('input[name="q"]');
        const box = await searchBox.boundingBox();
        if (box) {
            await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
            await page.mouse.click(box.x + box.width / 2, box.y + box.height / 2);
        }

        await new Promise(res => setTimeout(res, randInt(80, 300)));

        await page.type('input[name="q"]', productName, { delay: randInt(50, 110) });
        await page.keyboard.press('Enter');

        await page.waitForSelector('.pg-prf-head', { timeout: 70000 });

        await randomScroll(page);
        await randomMouseMoves(page, randInt(1, 3));

        const products = await page.evaluate(() => {
            return Array.from(document.querySelectorAll('a.name.clamp-2')).map(a => ({
                name: a.innerText.trim(),
                url: a.href.startsWith('http') ? a.href : `https://www.smartprix.com${a.getAttribute('href')}`,
            }));
        });

        await page.close();
        const matchedProducts = await matchProductName(productName, products);
        console.log('matchedProducts',matchedProducts)
        if(matchedProducts.length === 0) {
            console.log(`Matching not found: ${productName}`);
        } else {
            console.log(`Success: Matched ${productName} : ${matchedProducts}`);
        }

        return matchedProducts.length > 0 ? matchedProducts[0] : null;
    } catch (err) {
        await page.close();
        console.log(`Skipping ${productName} due to error: ${err.message}`);
        return null;
    }
}

export async function fetchProductDetail(product, productName, productId) {
    const page = await getPage();
    try {
        await page.goto(product.url, { waitUntil: 'domcontentloaded', timeout: 70000 });

        await randomScroll(page);
        await randomMouseMoves(page, randInt(1, 2));

        const productDetailHTML = await page.content();
        await page.close();
        const productInfo = await fetchProductInfo(productDetailHTML);
        productInfo.product_name = productName;
        productInfo.product_id = productId;
        return productInfo;
    } catch (err) {
        await page.close();
        console.log(`Skipping detail fetch for ${product?.url || ''} due to error: ${err.message}`);
        return null;
    }
}

export async function getProductInfo() {
    try {
        let processed = 0;
        for (let product = 0; product < PRODUCTS.length; product++) {
            try {
                processed++;

                // Restarting browsers helps avoid detection by websites
                if (processed % 5 === 0) {
                    await closeBrowser();
                    console.log("Restarting browser: ", processed);
                    await new Promise(res => setTimeout(res, 1000));
                }

                const matchedProduct = await searchProduct(PRODUCTS[product].product_name);
                console.log('product not found man before continue')
                if (!matchedProduct) continue;
                console.log('product not found man after continue')
                const productDetailInfo = await fetchProductDetail(matchedProduct, PRODUCTS[product].product_name, PRODUCTS[product].id);
                if (!productDetailInfo) continue;
                console.log(productDetailInfo);
                
                await uploadProductInfo(productDetailInfo, PRODUCTS[product].id);
                console.log("Wait 0.5 secs...");
                await new Promise(res => setTimeout(res, 500));
                
            } catch (innerErr) {
                console.log('Fatal error on product, resetting browser:', innerErr.message);
                await closeBrowser();
                await new Promise(res => setTimeout(res, 2000));
            }
        }
        await closeBrowser();
        process.exit(0);
    } catch (error) {
        console.log(error);
        await closeBrowser();
        process.exit(1);
    }
}
