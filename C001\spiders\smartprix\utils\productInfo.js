import { getPage, closeBrowser } from '../config/puppeterInstance.js';
import { PRODUCTS } from '../settings.js'
import { fetchProductInfo } from './cheerio.js';
import { randomScroll, randomMouseMoves, randInt, matchProductName, uploadProductInfo } from './util.js';

async function searchProduct(productName) {
    const page = await getPage();
    try {
        await page.goto('https://www.smartprix.com/', { waitUntil: 'domcontentloaded', timeout: 70000 });
        await page.waitForSelector('input[name="q"]', { timeout: 70000 });

        await randomMouseMoves(page, randInt(2, 5));
        await randomScroll(page);

        const searchBox = await page.$('input[name="q"]');
        const box = await searchBox.boundingBox();
        if (box) {
            await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
            await page.mouse.click(box.x + box.width / 2, box.y + box.height / 2);
        }

        await new Promise(res => setTimeout(res, randInt(80, 300)));

        await page.type('input[name="q"]', productName, { delay: randInt(50, 110) });
        await page.keyboard.press('Enter');

        // Wait for either results page or no-results page
        try {
            await Promise.race([
                page.waitForSelector('.pg-prf-head', { timeout: 15000 }),
                page.waitForSelector('.no-results, .noresult, .empty-state', { timeout: 15000 }),
                page.waitForFunction(() => {
                    return document.body.innerText.includes('No products found') ||
                           document.body.innerText.includes('no results') ||
                           document.body.innerText.includes('Sorry, no products found') ||
                           document.body.innerText.includes('No Products matched your query') ||
                           document.body.innerText.includes('No products matched your query');
                }, { timeout: 15000 })
            ]);
        } catch (timeoutError) {
            // If neither results nor no-results appear, wait a bit more for page to load
            await new Promise(res => setTimeout(res, 3000));
        }

        // Check if we actually have results
        const hasResults = await page.evaluate(() => {
            const resultsHeader = document.querySelector('.pg-prf-head');
            const productLinks = document.querySelectorAll('a.name.clamp-2');

            // Also check for "no results" text patterns
            const bodyText = document.body.innerText.toLowerCase();
            const hasNoResultsText = bodyText.includes('no products matched') ||
                                   bodyText.includes('no products found') ||
                                   bodyText.includes('no results');

            return resultsHeader && productLinks.length > 0 && !hasNoResultsText;
        });

        if (!hasResults) {
            console.log(`No search results found for: ${productName}`);
            await page.close();
            return null;
        }

        await randomScroll(page);
        await randomMouseMoves(page, randInt(1, 3));

        const products = await page.evaluate(() => {
            return Array.from(document.querySelectorAll('a.name.clamp-2')).map(a => ({
                name: a.innerText.trim(),
                url: a.href.startsWith('http') ? a.href : `https://www.smartprix.com${a.getAttribute('href')}`,
            }));
        });

        await page.close();

        // Additional check: if no products extracted, return null early
        if (!products || products.length === 0) {
            console.log(`No products extracted from search results for: ${productName}`);
            return null;
        }

        const matchedProducts = matchProductName(productName, products);

        if(matchedProducts.length === 0) {
            console.log(`Matching not found: ${productName}`);
        } else {
            console.log(`Success: Matched ${productName} : ${matchedProducts[0].name}`);
        }

        return matchedProducts.length > 0 ? matchedProducts[0] : null;
    } catch (err) {
        // Ensure page is closed even if it wasn't created properly
        try {
            await page.close();
        } catch (closeErr) {
            console.log(`Error closing page: ${closeErr.message}`);
        }
        console.log(`Skipping ${productName} due to error: ${err.message}`);
        return null;
    }
}

export async function fetchProductDetail(product, productName, productId) {
    const page = await getPage();
    try {
        await page.goto(product.url, { waitUntil: 'domcontentloaded', timeout: 70000 });

        await randomScroll(page);
        await randomMouseMoves(page, randInt(1, 2));

        const productDetailHTML = await page.content();
        await page.close();
        const productInfo = await fetchProductInfo(productDetailHTML);
        productInfo.product_name = productName;
        productInfo.product_id = productId;
        return productInfo;
    } catch (err) {
        await page.close();
        console.log(`Skipping detail fetch for ${product?.url || ''} due to error: ${err.message}`);
        return null;
    }
}

export async function getProductInfo() {
    try {
        let processed = 0;
        for (let product = 0; product < PRODUCTS.length; product++) {
            try {
                processed++;

                // Restarting browsers helps avoid detection by websites
                if (processed % 5 === 0) {
                    await closeBrowser();
                    console.log("Restarting browser: ", processed);
                    await new Promise(res => setTimeout(res, 1000));
                }

                console.log(`Processing product ${processed}/${PRODUCTS.length}: ${PRODUCTS[product].product_name}`);
                const matchedProduct = await searchProduct(PRODUCTS[product].product_name);
                if (!matchedProduct) {
                    console.log("product not found man before continue");
                    continue;
                }
                const productDetailInfo = await fetchProductDetail(matchedProduct, PRODUCTS[product].product_name, PRODUCTS[product].id);
                if (!productDetailInfo) continue;
                console.log(productDetailInfo);
                
                await uploadProductInfo(productDetailInfo, PRODUCTS[product].id);
                console.log("Wait 0.5 secs...");
                await new Promise(res => setTimeout(res, 500));
                
            } catch (innerErr) {
                console.log('Fatal error on product, resetting browser:', innerErr.message);
                await closeBrowser();
                await new Promise(res => setTimeout(res, 2000));
            }
        }
        await closeBrowser();
        process.exit(0);
    } catch (error) {
        console.log(error);
        await closeBrowser();
        process.exit(1);
    }
}
