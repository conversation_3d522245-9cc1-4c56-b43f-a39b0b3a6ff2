import { Storage as rdStorage } from '@google-cloud/storage';
const rdStorageClient = new rdStorage({ projectId: "reviewdale", keyFilename: './google-cloud-key-media.json' });
import { basename, join } from 'path';
import { existsSync, mkdirSync } from 'fs';

const artifactSourceBucket = 'rd-strg-app-devops';
async function downloadArtifact(artifactSourceBucket, artifactName, destinationArtifactName) {
    const options = {
        destination: destinationArtifactName,
    };
    await rdStorageClient.bucket(artifactSourceBucket).file(artifactName).download(options);
    console.log(`File ${artifactName} downloaded to ${destinationArtifactName} successfully.`);
}

async function listFiles(artifactSourceBucket, folderPath) {
    const [files] = await rdStorageClient.bucket(artifactSourceBucket).getFiles({
        prefix: folderPath + '/',
    });
    return files.map(file => file.name);
}

const folderPath = 'scripts/rd-app-srvc-common'
const localFolderPath = 'rd-build-artifacts'
async function downloadFolder(artifactSourceBucket, folderPath) {
    const files = await listFiles(artifactSourceBucket, folderPath);

    if (!existsSync(localFolderPath)) {
        mkdirSync(localFolderPath);
    }

    for (const file of files) {
        const fileName = basename(file);
        const destination = join(localFolderPath, fileName);
        await downloadArtifact(artifactSourceBucket, file, destination);
    }
}

downloadFolder(artifactSourceBucket, folderPath)
    .then(() => console.log('Folder downloaded successfully.'))
    .catch(err => console.error('Error downloading folder:', err));