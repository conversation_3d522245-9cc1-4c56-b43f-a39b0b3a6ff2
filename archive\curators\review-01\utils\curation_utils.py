import re
from datetime import datetime
import pytz

def extract_review_value(entity_value_raw):
    try:
        if entity_value_raw:
            entity_value_match = re.search(r"(\d+\.\d+)", entity_value_raw)
            if entity_value_match:
                return entity_value_match.group(1)
        return None
    except Exception as e:
        print(f"Error extracting entity value: {e}")
        return None

def convert_review_value_to_number(review_value_string):
    number_map = {"one": 1, "two": 2, "three": 3, "four": 4, "five": 5}
    if not review_value_string:
        return None
    digit_match = re.search(r"\d+", review_value_string)
    if digit_match:
        return int(digit_match.group())
    for word, number in number_map.items():
        if word in review_value_string.lower():
            return number
    return None

def sanitize_review(review_text):
    result = re.sub(r'amazon', 'seller', review_text, flags=re.IGNORECASE)
    return result

def convert_ist_to_utc(date_string):
    try:
        date_match = re.search(r"(\d{1,2} \w+ \d{4})", date_string)
        if date_match:
            date_str = date_match.group(1)
            date_obj = datetime.strptime(date_str, "%d %B %Y")
            ist = pytz.timezone("Asia/Kolkata")
            ist_date = ist.localize(date_obj)
            utc_date = ist_date.astimezone(pytz.utc)
            return utc_date.strftime("%Y-%m-%d %H:%M:%S %Z")
        return None
    except Exception as e:
        print(f"Error extracting date: {e}")
        return None

def extract_review_details(raw_review_data):
    try:
        product_reviews = {}
        product_reviews["product_id"] = raw_review_data.get("productInfo", {}).get(
            "productId", None
        )
        product_reviews["product_name"] = raw_review_data.get("productInfo", {}).get(
            "phoneName", None
        )
        if product_reviews["product_name"]:
            product_reviews["product_name"] = product_reviews["product_name"].strip()
        product_reviews["product_value"] = extract_review_value(
            raw_review_data.get("reviewValue", None)
        )
        reviews = []
        raw_reviews = raw_review_data.get("reviews", [])
        for review in raw_reviews:
            review_obj = {}
            review_obj["username"] = review.get("profileName", None)
            review_obj["review_value"] = extract_review_value(
                review.get("starRating", None)
            )
            review_obj["upvotes"] = convert_review_value_to_number(
                review.get("upvotes", None)
            )
            review_obj["review_date"] = convert_ist_to_utc(
                review.get("reviewDate", None)
            )
            review_obj["user_text"] = sanitize_review(review.get("reviewText", None))
            reviews.append(review_obj)

        product_reviews["reviews"] = reviews
        return product_reviews
    except Exception as e:
        print(f"Error extracting info: {e}")
        return None
