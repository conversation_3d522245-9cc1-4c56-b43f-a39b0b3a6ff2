from bs4 import BeautifulSoup
import os
import json
import base64
import requests

BASE_URL = 'https://www.91mobiles.com/phonefinder.php/template/category_finder/finder_ajax.php?page=1&category=mobile'

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.9',
    'Connection': 'keep-alive',
    'Referer': 'https://www.91mobiles.com/phonefinder.php',
    'Cookie': '__UT91mobiles=16261302496746a53ac43914.15880359; initialPageSource=direct; PHPSESSID=ip-172-16-160-95.91mobiles.net~gal8tj17omnh6ki7a6nv4nrps4'
}


def get_home_page_url(page):
    return f'https://www.91mobiles.com/template/category_finder/finder_ajax.php?ord=0.297044338904773&excludeId=&hash=&search=&hidFrmSubFlag=1&page=1&category=mobile&unique_sort=&gaCategory=PhoneFinder-filter&requestType=2&showPagination=1&listType=list&listType_v3=list&listType_v1=list&listType_v2=list&listType_v4=list&listType_v5=list&listType_v6=list&page_type=finder&finderRuleUrl=&selMobSort=views&hdnCategory=mobile&user_search=&url_feat_rule=&buygaCat=finder-mob&isCustomAuto=0&buygaCat=finder-mob&isCustomAuto=0&hdndprice=25001-25000%2C30001-100000&hdndprice1=15001-25000&amount=0%3B65000&sCatName=mobile&price_range_apply=0&request_uri=/phonefinder.php'


def scrape_home_page_content(url):
    try:
        response = requests.get(url, headers=HEADERS)
        return response.text
    except requests.exceptions.RequestException as e:
        print('Error fetching base URL:', str(e))

def get_details_page_links(data):
    try:
        soup = BeautifulSoup(data, 'html.parser')
        links = ['https://www.91mobiles.com' + a['href'] for a in
                 soup.find_all('a', class_='hover_blue_link name gaclick')]
        return links
    except Exception as e:
        print(f'ERROR :: while extracting detail page links {e}')
        return []


def extract_product_attributes(link):
    try:
        response = requests.get(link, headers=HEADERS)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        phone_name = soup.select_one('h1.h1_pro_head').text.strip() if soup.select_one('h1.h1_pro_head') else ''
        rating_text = soup.select_one('.ratpt').text.strip() if soup.select_one('.ratpt') else '0.0'
        rating = float(rating_text.split(' ')[0])
        price_element = soup.select_one('.store_prc')
        price = price_element.text.strip() if price_element else ''

        product_image_element = soup.select_one('img.overview_lrg_pic_img')
        product_image = product_image_element['src'] if product_image_element else ''

        specs = []
        for spec_box in soup.select('.spec_box'):
            for tr in spec_box.select('.spec_table tr'):
                title = tr.select_one('.spec_ttle').text.strip() if tr.select_one('.spec_ttle') else ''
                description = tr.select_one('.spec_des').text.strip() if tr.select_one('.spec_des') else ''
                specs.append({'title': title, 'description': description})

        images = [img['data-src'] for img in soup.select('img.alb_img.lazy-loaded')]

        return {
            'phone_name': phone_name,
            'rating': rating,
            'price': price,
            'product_image': product_image,
            'specs': specs,
            'images': images
        }
    except Exception as e:
        print(f'ERROR :: while extracting product attributes {e}')
        return {}


def export_raw_data(file_path, data):
    try:
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
        print(f"Data exported successfully to {file_path}")
    except Exception as e:
        print(f"ERROR :: while exporting data {e}")
