import sqlite3
from datetime import datetime
import random
import platform
from utils.util import generate_review_metrics
from rd_df_Common.rd_id_generation import generate_entity_id

if platform.system() == 'Linux':
    connection_pool = sqlite3.connect(
        # TODO: Make path correct
        r"root\reviewdale\df-dh\rd-df-C001.db3" 
    )
else:
    connection_pool = sqlite3.connect(
        r"C:\reviewdale\df-dh\rd-df-C001.db3"
    )
cursor = connection_pool.cursor()

def insert_reviews(reviews_json):
    try:
        product_id = reviews_json["product_id"]
        reviews = reviews_json["reviews"]
        brand_id = "B" + product_id[4:8]
        insert_query = """
        INSERT INTO cch_mstr_review (
            id, user_id, brand_id, product_id, user_text, review_value,
            create_date, is_active, category_id, type_id, upvotes, 
            review_metric_1_value, review_metric_2_value, review_metric_3_value, 
            review_metric_4_value, category_review_metric_1_value, 
            category_review_metric_2_value
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        for review in reviews:
            username = review["username"]
            cursor.execute("SELECT id FROM cch_mstr_user WHERE username = ?", (username,))
            user_id = cursor.fetchone()

            review_value = review.get("review_value")
            if review_value is not None:
                review_metrics = generate_review_metrics(float(review_value))
            else:
                review_metrics = [None] * 6

            if user_id is None:
                cursor.execute(
                    "INSERT INTO cch_mstr_user (username, type_id) VALUES (?, 1)",
                    (username,),
                )
                connection_pool.commit()
                user_id = cursor.lastrowid
            else:
                user_id = user_id[0]

            review_id = generate_entity_id('review')
            cursor.execute(
                insert_query,
                (
                    review_id,
                    user_id,
                    brand_id,
                    product_id,
                    review.get("user_text"),
                    review_value,
                    review.get("review_date") or datetime.now(),
                    1,
                    "C001",
                    1,
                    review.get("upvotes"),
                    review_metrics[0],
                    review_metrics[1],
                    review_metrics[2],
                    review_metrics[3],
                    review_metrics[4],
                    review_metrics[5],
                ),
            )
        connection_pool.commit()
        print("Reviews inserted successfully!")
    except Exception as e:
        print(f"An error occurred: {e}")
        connection_pool.rollback()
