import json
from dotenv import load_dotenv # type: ignore
import os
from google.cloud import storage

load_dotenv()

class JsonWriterPipeline:
    def __init__(self):
        self.env = os.getenv('ENV', 'dev')
        self.files = {}
        self.gcs_bucket_name = os.getenv('GCS_BUCKET_NAME', '')
        self.gcs_folder = os.getenv('GCS_FOLDER', '')
        self.seen_review_ids = set() 

    def open_spider(self, spider):
        self.files = {}
        self.seen_review_ids = set()

    def close_spider(self, spider):
        for product, file in self.files.items():
            file.write(']')
            file.close()

            if self.env != 'dev':
                self.upload_to_gcs(product)

    def process_item(self, item, spider):
        review_id = item.get('review_id')

        if not review_id or review_id in self.seen_review_ids:
            return None

        self.seen_review_ids.add(review_id)

        product = item['product_id']
        filename = f'{product}.json'

        if product not in self.files:
            self.files[product] = open(filename, 'w', encoding='utf-8')
            self.files[product].write('[')
        else:
            self.files[product].write(',')

        line = json.dumps(dict(item), ensure_ascii=False, indent=4)
        self.files[product].write('\n' + line)
        self.files[product].flush() 
        return item


    def upload_to_gcs(self, product):
        filename = f'{product}.json'
        blob_name = f'{self.gcs_folder}/{filename}' if self.gcs_folder else filename

        try:
            client = storage.Client()
            bucket = client.bucket(self.gcs_bucket_name)
            blob = bucket.blob(blob_name)
            blob.upload_from_filename(filename)
            print(f"Uploaded {filename} to GCS: {blob_name}")
            os.remove(filename)
        except Exception as e:
            print(f"Failed to upload {filename} to GCS: {e}")
