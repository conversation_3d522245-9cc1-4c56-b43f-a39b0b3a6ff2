from data_pump.db_config import connection_pool, get_connection_from_pool
from datetime import datetime, timezone
import mysql  # type: ignore
import re
from decimal import Decimal
import logging
import json
from queries import GET_BRAND_BY_ID, GET_BRAND_BY_NAME, GET_PRODUCT_BY_ID, INGEST_PRODUCT_IMAGES_QUERY, INGEST_BRAND_QUERY, GET_ALL_REVIEW_IDS_QUERY, INGEST_PRODUCT_PRICES_QUERY, INGEST_PRODUCT_QUERY, INGEST_REVIEWS_QUERY

create_timestamp = datetime.now(timezone.utc)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_existing_review_ids(source_connect):
    try:
        with source_connect.cursor() as conn_cursor:
            all_review_ids = GET_ALL_REVIEW_IDS_QUERY
            conn_cursor.execute(all_review_ids)
            all_cursors = conn_cursor.fetchall()
            review_id_list = [row[0] for row in all_cursors]
            return review_id_list
    except Exception as e:
        logger.info(f"Error: Get Existing Review Ids: {e}")
        return []

def ingest_reviews(review_list, conn):
    try:
        with conn.cursor() as cursor:
            ingest_reviews_query = INGEST_REVIEWS_QUERY
            REVIEW_ROW_KEYS = (
                'id',
                'product_id',
                'review_text',
                'review_value',
                'date',
                'upvotes',
                'downvotes',
                'product_url',
                'review_id',
            )
            
            review_values = [
                tuple(row[key] for key in REVIEW_ROW_KEYS)
                for row in review_list
            ]

            cursor.executemany(ingest_reviews_query, review_values)
        conn.commit()
        logger.info("Success: Reviews inserted.")
    except Exception as e:
        logger.info(f"Failed: Insert reviews with error: {e}")

def ingest_product_dataset(product_dataset):
    logger.info(product_dataset)
    with get_connection_from_pool(connection_pool) as conn:
        if conn is None:
            logger.error("Failed: Connect to rd db")
            return

        with conn.cursor() as cursor:
            try:
                ingest_product_details(cursor, product_dataset)
                ingest_product_prices(cursor, product_dataset)
                ingest_product_images(cursor, product_dataset)
                conn.commit()
                logger.info("Success: Products inserted.")
            except mysql.connector.Error as err:
                logger.error(f"Error: Product ingestion with: {err}")
                conn.rollback()

def ingest_product_details(cursor, product_dataset):
    ingest_product_query = INGEST_PRODUCT_QUERY

    product_values = [
        (
            row['product_id'], row['product_id'], row['external_id'], row['brand_id'], row['brand'], row['product_value'],
            row['model'], row['series'], row['variant'],
            json.dumps(row.get('attribute_1', {})), json.dumps(row.get('attribute_2', {})), json.dumps(row.get('attribute_3', {})),
            json.dumps(row.get('attribute_4', {})), json.dumps(row.get('attribute_5', {})), json.dumps(row.get('attribute_7', {})),
            json.dumps(row.get('attribute_8', {})), json.dumps(row.get('attribute_9', {})), json.dumps(row.get('attribute_10', {})),
            json.dumps(row.get('attribute_11', {})), json.dumps(row.get('attribute_12', {})), json.dumps(row.get('attribute_13', {})),
            json.dumps(row.get('attribute_14', {})), json.dumps(row.get('attribute_15', {})), json.dumps(row.get('attribute_16', {})),
            json.dumps(row.get('attribute_17', {})), json.dumps(row.get('attribute_18', {})), json.dumps(row.get('attribute_19', {})),
            json.dumps(row.get('attribute_20', {})), json.dumps(row.get('attribute_21', {})), json.dumps(row.get('attribute_22', {})),
            json.dumps(row.get('attribute_23', {})), json.dumps(row.get('attribute_24', {})), create_timestamp
        )
        for row in product_dataset
    ]
    cursor.executemany(ingest_product_query, product_values)

def ingest_product_prices(cursor, product_dataset):
    try:
        if not product_dataset:
            return

        ingest_product_prices_query = INGEST_PRODUCT_PRICES_QUERY

        product_price_values = [
            (row['product_id'], row['price'])
            for row in product_dataset
        ]
        cursor.executemany(ingest_product_prices_query, product_price_values)
    except AttributeError as e:
        logging.error(f"Failed: Ingest Product Prices with: {e}")

def ingest_product_images(cursor, product_dataset):
    try:
        if not product_dataset:
            logging.error("Failed: Product Images ingestion: Received empty or None dataset")
            return

        ingest_product_images_query = INGEST_PRODUCT_IMAGES_QUERY

        product_image_values = []
        for row in product_dataset:
            if not isinstance(row, dict):
                logging.warning(f"Warning: Skipping non-dictionary row: {row}")
                continue

            product_id = row.get("product_id", "")
            create_date = datetime.today().strftime('%Y-%m-%d')

            for image_url in row.get("product_images", []):
                if not image_url:
                    continue
                description = None
                match = re.search(r'(\d+)\.png$', image_url)
                if match:
                    identifier = int(match.group(1))
                    description = identifier if identifier in [1, 2] else None
                else:
                    identifier = description
                product_image_values.append((product_id, description, image_url, create_timestamp, identifier))

        if product_image_values:
            cursor.executemany(ingest_product_images_query, product_image_values)
        else:
            logging.info("Info: No valid product images.")
    except AttributeError as e:
        logging.error(f"Failed: Product Images ingestion with: {e}")

def brand_id_exists(brand_id):
    try:
        with get_connection_from_pool(connection_pool) as conn:
            if conn is None:
                logger.error("Failed: Connect to rd db.")
                return None

            with conn.cursor() as cursor:
                get_brand_id = GET_BRAND_BY_ID
                cursor.execute(get_brand_id, (brand_id,))
                result = cursor.fetchone()
                return result
    except AttributeError as e:
        logging.error(f"Failed: Brand Id already exists with: {e}")

def get_brand_id_by_name(brand_name):
    with get_connection_from_pool(connection_pool) as conn:
        if conn is None:
            logger.error("Failed: Connect to rd db")
            return None

        with conn.cursor() as cursor:
            get_brand = GET_BRAND_BY_NAME
            cursor.execute(get_brand, (brand_name,))
            result = cursor.fetchone()
            return result

def product_id_exists(product_id):
    with get_connection_from_pool(connection_pool) as conn:
        if conn is None:
            logger.error("Failed: Connect to rd db")
            return None

        with conn.cursor() as cursor:
            get_product = GET_PRODUCT_BY_ID
            cursor.execute(get_product, (product_id,))
            result = cursor.fetchone()
            return result

def ingest_brand_if_new(brand_id, brand_name):
    with get_connection_from_pool(connection_pool) as conn:
        if conn is None:
            logger.error("Failed: Connect to rd db")
            return

        with conn.cursor() as cursor:
            try:
                ingest_brands_query = INGEST_BRAND_QUERY
                create_date = datetime.today().strftime('%Y-%m-%d')
                cursor.execute(ingest_brands_query, (brand_id, brand_name, create_timestamp))
                conn.commit()
                logger.info(f"Info: New Brand inserted: {brand_id}:{brand_name}")
            except mysql.connector.Error as err:
                logger.error(f"Error: Brand insertion with: {err}")
                conn.rollback()
