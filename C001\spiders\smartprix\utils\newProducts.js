import { getPage, closeBrowser } from '../config/puppeterInstance.js';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { fetchProductDetail } from './productInfo.js';
import { mkdir } from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const getNewProducts = async () => {
    try {
        let batch = 1;
        let lastProductCount = 0;
        const page = await getPage();

        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36');
        await page.setExtraHTTPHeaders({ 'accept-language': 'en-US,en;q=0.9' });
        await page.goto('https://www.smartprix.com/mobiles/3_months-launched_within?sort=date&asc=0', { waitUntil: 'domcontentloaded' });
        await page.waitForSelector('.sm-product', { timeout: 15000 });

        while (true) {
            const products = await page.evaluate(() => {
                return Array.from(document.querySelectorAll('a.name.clamp-2')).map(a => ({
                    name: a.innerText.trim(),
                    url: a.href.startsWith('http') ? a.href : `https://www.smartprix.com${a.getAttribute('href')}`
                }));
            });

            console.log(`Batch ${batch}: Total products in DOM =`, products.length);

            const newProducts = products.slice(lastProductCount);
            if (newProducts.length > 0) {
                for (const newProduct of newProducts) {
                    const newProductInfo = await fetchProductDetail({ url: newProduct.url, product_name: newProduct.name, product_id: newProduct.name }, newProduct.name, newProduct.name)
                    

                    const filename = `../response/smartprix-response/${newProduct.name}.json`;

                    // Replace the writeFile section with this:
                    const responseDir = path.join(__dirname, '../response/smartprix-response');
                    await mkdir(responseDir, { recursive: true }); // This will create the directory if it doesn't exist
                    await fs.writeFile(path.join(responseDir, `${newProduct.name}.json`), JSON.stringify(newProductInfo, null, 2), 'utf8');

                    console.log(`New product ${newProduct.name} written to ${filename}`);
                }

                batch++;
            } else {
                console.log(`Batch ${batch}: No new products found, stopping.`);
                break;
            }

            lastProductCount = products.length;

            const loadMore = await page.$('.sm-load-more');
            if (!loadMore) break;
            await loadMore.click();

            let tries = 0;
            while (tries < 15) {
                await new Promise(res => setTimeout(res, 1000));
                const newCount = await page.evaluate(() => document.querySelectorAll('a.name.clamp-2').length);
                if (newCount > lastProductCount) break; // New products loaded!
                tries++;
            }
        }

        await closeBrowser();
    } catch (error) {
        console.log(error);
    }
}