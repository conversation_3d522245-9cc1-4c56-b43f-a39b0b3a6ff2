import { getPage, closeBrowser } from '../config/puppeterInstance.js';
import { fetchProductDetail } from './productInfo.js';
import { getProductDataset, matchProductName, uploadProductInfo, randInt } from './util.js';

/**
 * Check if a scraped product already exists in the centralized dataset
 * @param {string} scrapedProductName - Name of the product scraped from Smartprix
 * @param {Array} existingProducts - Array of existing products from centralized dataset
 * @returns {boolean} - True if product already exists, false if it's new
 */
function isProductAlreadyExists(scrapedProductName, existingProducts) {
    // Convert existing products to the format expected by matchProductName
    // Handle different possible data structures from centralized dataset
    const existingProductsFormatted = existingProducts.map(product => ({
        name: product.product_name || product.name || product.title || '',
        id: product.id || product.product_id || ''
    }));

    // Use the existing matchProductName function to find matches
    const matches = matchProductName(scrapedProductName, existingProductsFormatted);

    return matches.length > 0;
}

/**
 * Generate a unique product ID for new products
 * @param {string} productName - Name of the product
 * @returns {string} - Generated unique product ID
 */
function generateNewProductId(productName) {
    // Create a timestamp-based ID with product name hash
    const timestamp = Date.now();
    const nameHash = productName.replace(/[^a-zA-Z0-9]/g, '').substring(0, 10);
    return `P${timestamp}${nameHash}001`;
}

/**
 * Get current date in YYYY-MM-DD format for folder naming
 * @returns {string} - Current date string
 */
function getCurrentDateString() {
    const now = new Date();
    return now.toISOString().split('T')[0];
}

export const getNewProducts = async () => {
    try {
        console.log('🚀 Starting GCP-Only New Product Discovery Process...');
        console.log('🌐 This system operates exclusively with GCP Storage - no local storage or fallbacks');

        // Fetch centralized product dataset from GCP
        console.log('📊 Loading centralized product dataset from GCP...');
        const centralizedProducts = await getProductDataset();
        console.log(`📋 Loaded ${centralizedProducts.length} products from centralized GCP dataset`);

        // Statistics tracking
        let totalScrapedProducts = 0;
        let newProductsFound = 0;
        let existingProductsSkipped = 0;
        let errorCount = 0;

        let batch = 1;
        let lastProductCount = 0;
        const page = await getPage();

        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
        await page.setExtraHTTPHeaders({ 'accept-language': 'en-US,en;q=0.9' });
        await page.goto('https://www.smartprix.com/mobiles/3_months-launched_within?sort=date&asc=0', { waitUntil: 'domcontentloaded' });
        await page.waitForSelector('.sm-product', { timeout: 15000 });

        while (true) {
            const products = await page.evaluate(() => {
                return Array.from(document.querySelectorAll('a.name.clamp-2')).map(a => ({
                    name: a.innerText.trim(),
                    url: a.href.startsWith('http') ? a.href : `https://www.smartprix.com${a.getAttribute('href')}`
                }));
            });

            console.log(`📦 Batch ${batch}: Total products in DOM = ${products.length}`);

            const newProducts = products.slice(lastProductCount);
            if (newProducts.length > 0) {
                console.log(`🔍 Processing ${newProducts.length} products from batch ${batch}...`);

                for (const scrapedProduct of newProducts) {
                    try {
                        totalScrapedProducts++;

                        // Check if this product already exists in centralized dataset
                        const alreadyExists = isProductAlreadyExists(scrapedProduct.name, centralizedProducts);

                        if (alreadyExists) {
                            existingProductsSkipped++;
                            console.log(`⏭️  SKIPPED (exists): ${scrapedProduct.name}`);
                            continue;
                        }

                        // This is a new product! Process it
                        newProductsFound++;
                        const newProductId = generateNewProductId(scrapedProduct.name);

                        console.log(`🆕 NEW PRODUCT FOUND: ${scrapedProduct.name}`);
                        console.log(`   Generated ID: ${newProductId}`);

                        // Fetch detailed product information
                        const newProductInfo = await fetchProductDetail(
                            { url: scrapedProduct.url, product_name: scrapedProduct.name, product_id: newProductId },
                            scrapedProduct.name,
                            newProductId
                        );

                        if (!newProductInfo) {
                            errorCount++;
                            console.log(`❌ Failed to fetch details for: ${scrapedProduct.name}`);
                            continue;
                        }

                        // Add metadata to distinguish new products
                        newProductInfo.is_new_discovery = true;
                        newProductInfo.discovery_date = new Date().toISOString();
                        newProductInfo.discovery_batch = batch;

                        // Save to GCP bucket ONLY (no local storage)
                        const currentDate = getCurrentDateString();
                        await uploadProductInfo(
                            newProductInfo,
                            newProductId,
                            "rd-bckt-df-data-harvester",
                            `C001/new-products/${currentDate}`
                        );

                        console.log(`✅ NEW PRODUCT SAVED TO GCP: ${scrapedProduct.name} (ID: ${newProductId})`);

                        // Add delay to be respectful to the server
                        await new Promise(res => setTimeout(res, randInt(500, 1000)));

                    } catch (productError) {
                        errorCount++;
                        console.log(`❌ Error processing product ${scrapedProduct.name}: ${productError.message}`);
                    }
                }

                batch++;
            } else {
                console.log(`📭 Batch ${batch}: No new products found, stopping.`);
                break;
            }

            lastProductCount = products.length;

            const loadMore = await page.$('.sm-load-more');
            if (!loadMore) {
                console.log(`🔚 No more "Load More" button found. Reached end of products.`);
                break;
            }

            console.log(`⏳ Loading more products...`);
            await loadMore.click();

            let tries = 0;
            while (tries < 15) {
                await new Promise(res => setTimeout(res, 1000));
                const newCount = await page.evaluate(() => document.querySelectorAll('a.name.clamp-2').length);
                if (newCount > lastProductCount) break; // New products loaded!
                tries++;
            }

            if (tries >= 15) {
                console.log(`⚠️  Timeout waiting for new products to load. Stopping.`);
                break;
            }
        }

        await closeBrowser();

        // Print comprehensive statistics
        console.log('\n' + '='.repeat(60));
        console.log('📊 GCP-ONLY NEW PRODUCT DISCOVERY SUMMARY');
        console.log('='.repeat(60));
        console.log(`🔍 Total products scraped from Smartprix: ${totalScrapedProducts}`);
        console.log(`🆕 New products discovered: ${newProductsFound}`);
        console.log(`⏭️  Existing products skipped: ${existingProductsSkipped}`);
        console.log(`❌ Errors encountered: ${errorCount}`);
        console.log(`📈 Discovery rate: ${totalScrapedProducts > 0 ? ((newProductsFound / totalScrapedProducts) * 100).toFixed(2) : 0}%`);
        console.log(`🌐 New products saved to GCP: gs://rd-bckt-df-data-harvester/C001/new-products/${getCurrentDateString()}`);
        console.log(`📋 Centralized GCP dataset size: ${centralizedProducts.length} products`);
        console.log(`🚫 No local storage used - GCP-only operation`);
        console.log('='.repeat(60));

        if (newProductsFound > 0) {
            console.log(`🎉 SUCCESS: Discovered ${newProductsFound} new products and saved to GCP!`);
        } else {
            console.log(`ℹ️  No new products found. All scraped products already exist in centralized GCP dataset.`);
        }

    } catch (error) {
        console.log('❌ Fatal error in GCP-only new product discovery:', error.message);

        // Check if it's a GCP-related error
        if (error.message.includes('centralized product dataset') || error.message.includes('GCP')) {
            console.log('🌐 This system requires GCP Storage access to function.');
            console.log('📋 Ensure proper GCP authentication and network connectivity.');
            console.log('🚫 No local fallback available in GCP-only mode.');
        }

        await closeBrowser();
        throw error; // Re-throw to ensure calling code knows about the failure
    }
}