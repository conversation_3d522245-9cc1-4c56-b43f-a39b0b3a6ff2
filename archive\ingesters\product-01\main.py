import os
import json
import requests
from dotenv import load_dotenv
from util import get_home_page_url, scrape_home_page_content, get_details_page_links, extract_product_attributes, export_raw_data

load_dotenv()

AXIOS_TIMEOUT = int(os.getenv('AXIOS_TIMEOUT'))


def start_ingester():
    total_products = 0
    batch_count = 1
    TOTAL_BATCHES = 1

    while batch_count <= TOTAL_BATCHES:
        raw_batch_data = []
        home_page_url = get_home_page_url(batch_count)
        print(home_page_url)
        main_page_html = scrape_home_page_content(home_page_url)
        print("main content " + main_page_html)
        export_raw_data('test.txt', main_page_html)

        # If page content is empty, break the loop
        if not main_page_html:
            break

        detail_page_links = get_details_page_links(main_page_html)
        print(detail_page_links)
        for link in detail_page_links:
            product_data = extract_product_attributes(link)
            if product_data:
                raw_batch_data.append(product_data)

        # Export raw data to a local file
        file_name = f'local_data/C001-raw-data-{batch_count}.json'
        os.makedirs('local_data', exist_ok=True)
        export_raw_data(file_name, raw_batch_data)

        total_products += len(raw_batch_data)
        batch_count += 1

    print(f'Total products scraped: {total_products}')


if __name__ == "__main__":
    start_ingester()
