import random
from datetime import datetime
import platform
import sqlite3

if platform.system() == 'Linux':
    connection_pool = sqlite3.connect(
        # TODO: Make path correct
        r"root\reviewdale\df-dh\rd-df-C001.db3" 
    )
else:
    connection_pool = sqlite3.connect(
        r"C:\reviewdale\df-dh\rd-df-C001.db3"
    )
cursor = connection_pool.cursor()

def generate_product_id(category_id, brand_id, check_collision=False):
    try:
        category_id = category_id[1:]
        brand_id = brand_id[1:]
        prefix = f"P{category_id}{brand_id}"
        random_num = f"{random.randint(1, 9999):04d}"
        random_product_id = f"{prefix}{random_num}"
        if check_collision:
            if check_id_collision("product", random_product_id):
                return generate_product_id(category_id, brand_id, check_collision)
        return random_product_id
    except Exception as error:
        print(f"Error generating product ID: {error}")
        return None

def generate_entity_id(entity_type, check_collision=False):
    if entity_type == "review":
        generated_id = f"R{random.randint(0, 999999999):09d}"
    elif entity_type == "question":
        generated_id = f"Q{random.randint(0, 999999999):09d}"
    elif entity_type == "brand":
        generated_id = f"B{random.randint(0, 9999):04d}"
    else:
        raise ValueError(
            "Invalid entity type. Must be 'review', 'question', or 'brand'."
        )
    if check_collision:
        if check_id_collision(entity_type, generated_id):
            return generate_entity_id(entity_type, check_collision)
        else:
            return generated_id
    else:
        return generated_id

def check_id_collision(entity_type, generated_id):
    rd_table_mapping = {
        "review": ("cch_mstr_review", "id"),
        "question": ("cch_mstr_question", "id"),
        "brand": ("cch_mstr_brand", "id"),
        "product": ("cch_mstr_product", "product_id"),
    }
    table_info = rd_table_mapping.get(entity_type)
    if table_info is None:
        raise ValueError(
            f"Invalid entity type: {entity_type}. Must be 'review', 'question', 'brand', or 'product'."
        )
    table_name, id_field = table_info
    check_id_query = f"""
        SELECT {id_field}
        FROM {table_name}
        WHERE {id_field} = %s
        LIMIT 1
    """
    try:
        cursor.execute(check_id_query, (generated_id,))
        result = cursor.fetchone()
        return result is not None
    except Exception as e:
        print(f"Database error: {e}")
        return False
    finally:
        cursor.close()
