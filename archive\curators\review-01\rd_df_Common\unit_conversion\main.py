import pint
import json

units = pint.UnitRegistry()

standard_units = {
    'area': 'square_meter',
    'charge': 'ampere_hour',
    'current': 'ampere',
    'data': 'gigabyte',
    'energy': 'joule',
    'force': 'newton',
    'frequency': 'hertz',
    'length': 'meter',
    'luminous_intensity': 'candela',
    'magnetic_flux': 'weber',
    'magnetic_flux_density': 'tesla',
    'power': 'watt',
    'pressure': 'pascal',
    'speed': 'kilometer/hour',
    'temperature': 'celsius',
    'time': 'second',
    'volume': 'liter',
    'weight': 'kilogram'
}

def rd_convert_to_metric(value_with_unit):
    try:
        value, unit = value_with_unit.split()
        value = float(value)
    except (ValueError, AttributeError):
        return {"attribute_value": None, "attribute_unit": None} 
    
    quantity = value * units(unit)
    
    if quantity.check('[length]'):
        standard_quantity = quantity.to(standard_units['length'])
    elif quantity.check('[mass]'):
        standard_quantity = quantity.to(standard_units['weight'])
    elif quantity.check('[volume]'):
        standard_quantity = quantity.to(standard_units['volume'])
    elif quantity.check('[time]'):
        standard_quantity = quantity.to(standard_units['time'])
    elif quantity.check('[temperature]'):
        standard_quantity = quantity.to(standard_units['temperature'])
    elif quantity.check('[current]'):
        standard_quantity = quantity.to(standard_units['current'])
    elif quantity.check('[energy]'):
        standard_quantity = quantity.to(standard_units['energy'])
    elif quantity.check('[power]'):
        standard_quantity = quantity.to(standard_units['power'])
    elif quantity.check('[pressure]'):
        standard_quantity = quantity.to(standard_units['pressure'])
    elif quantity.check('[area]'):
        standard_quantity = quantity.to(standard_units['area'])
    elif quantity.check('[speed]'):
        standard_quantity = quantity.to(standard_units['speed'])
    elif quantity.check('[force]'):
        standard_quantity = quantity.to(standard_units['force'])
    elif quantity.check('[frequency]'):
        standard_quantity = quantity.to(standard_units['frequency'])
    elif quantity.check('[data]'):
        standard_quantity = quantity.to(standard_units['data'])
    elif quantity.check('[luminous_intensity]'):
        standard_quantity = quantity.to(standard_units['luminous_intensity'])
    elif quantity.check('[magnetic_flux]'):
        standard_quantity = quantity.to(standard_units['magnetic_flux'])
    elif quantity.check('[magnetic_flux_density]'):
        standard_quantity = quantity.to(standard_units['magnetic_flux_density'])
    else:
        raise ValueError("Invalid: Unit type")

    converted_value = standard_quantity.magnitude
    converted_unit = str(standard_quantity.units)
    
    output = {
        "attribute_value": converted_value,
        "attribute_unit": converted_unit
    }
    
    return json.dumps(output)