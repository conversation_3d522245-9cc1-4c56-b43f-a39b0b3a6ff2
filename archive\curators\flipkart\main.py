import os
import sys
import logging
from datetime import datetime, timedelta
import random
import json
import tempfile
import apache_beam as beam
from apache_beam.options.pipeline_options import PipelineOptions, SetupOptions
from ordered_set import OrderedSet
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

class ProcessReviewFile(beam.DoFn):
    def __init__(self, review_id_set):
        self.review_id_set = review_id_set

    def setup(self):
        from google.cloud import storage, secretmanager
        import mysql.connector

        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
        self.bucket_name = os.getenv('RD_DF_RAW_DATA_BUCKET_NAME', 'rd-bckt-df-data-harvester')
        self.input_dir = os.getenv('INPUT_DIR')

        self.rd_storage_client = storage.Client(project=self.project_id)
        self.secret_client = secretmanager.SecretManagerServiceClient()

        def access_secret(secret_id: str, version_id: str = "latest") -> str:
            name = f"projects/reviewdale-datafabric/secrets/{secret_id}/versions/{version_id}"
            response = self.secret_client.access_secret_version(request={"name": name})
            return response.payload.data.decode("UTF-8")

        def write_temp_file(contents: str, suffix: str = ".pem") -> str:
            temp = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
            temp.write(contents.encode("utf-8"))
            temp.flush()
            return temp.name

        db_config = {
            "database": access_secret("rd_db_stg_database"),
            "host": access_secret("rd_db_stg_host"),
            "user": access_secret("rd_db_stg_user"),
            "password": access_secret("rd_db_stg_password"),
            "ssl_ca": write_temp_file(access_secret("rd_db_stg_ssl_ca")),
            "ssl_cert": write_temp_file(access_secret("rd_db_stg_ssl_cert")),
            "ssl_key": write_temp_file(access_secret("rd_db_stg_ssl_key")),
        }

        self.conn_pool = mysql.connector.pooling.MySQLConnectionPool(
            pool_name="connection_pool",
            pool_size=5,
            pool_reset_session=True,
            **db_config
        )
        self.conn = self.conn_pool.get_connection()

    def process(self, input_file_path):
        try:
            bucket = self.rd_storage_client.bucket(self.bucket_name)
            blob = bucket.blob(input_file_path)
            if not blob.exists():
                raise FileNotFoundError(f"Not found: '{input_file_path}'.")
            raw_file = json.loads(blob.download_as_text())
            processed_file = curate_reviews(raw_file, self.review_id_set)
            ingest_reviews(processed_file, self.conn)
            yield f"Processed: {input_file_path}"
        except Exception as e:
            yield f"Failed: Processing {input_file_path} with: {str(e)}"

    def teardown(self):
        if hasattr(self, 'conn') and self.conn:
            self.conn.close()

def rd_generate_review_ids(raw_review_list, review_id_set):
    def generate_id():
        return 'R' + ''.join(random.choices('0123456789', k=9))
    for row in raw_review_list:
        while True:
            new_id = generate_id()
            if new_id not in review_id_set:
                review_id_set.add(new_id)
                row['id'] = new_id
                break
    return raw_review_list

def seed_rating_metrics(target_review_value):
    metrics = [round(random.uniform(max(1.0, target_review_value - 1.0), min(5.0, target_review_value + 1.0)), 1) for _ in range(5)]
    sum_of_metrics = sum(metrics)
    metrics_6 = round(6.0 * target_review_value - sum_of_metrics, 1)
    if metrics_6 < 1.0 or metrics_6 > 5.0:
        adjustment = round((metrics_6 - 3.0) / 5.0, 1)
        metrics = [round(min(max(1.0, num + adjustment), 5.0), 1) for num in metrics]
        metrics_6 = round(6.0 * target_review_value - sum(metrics), 1)
    metrics.append(metrics_6)
    return metrics

def generate_random_time(date_str):
    base_date = datetime.strptime(date_str, "%Y-%m-%d")
    random_time = timedelta(hours=random.randint(0, 23), minutes=random.randint(0, 59), seconds=random.randint(0, 59))
    return (base_date + random_time).strftime("%Y-%m-%d %H:%M:%S")

def curate_reviews(raw_review_list, review_id_set):
    input_review_list = rd_generate_review_ids(raw_review_list, review_id_set)
    for row in input_review_list:
        if row.get('product_url') is None:
            logger.info("Not found: Product link.")
            return []
        row['date'] = generate_random_time(row['date'])
    return input_review_list

def get_review_ids(conn):
    try:
        with conn.cursor() as cursor:
            cursor.execute("SELECT id FROM df_ppl_review")
            id_list = cursor.fetchall()
            return [row[0] for row in id_list]
    except Exception as e:
        logger.info(f"Failed: Get Review Ids with: {e}")
        return []

def ingest_reviews(processed_file, conn):
    try:
        with conn.cursor() as cursor:
            ingest_reviews_query = """
                INSERT INTO df_ppl_review (
                    id, product_id, user_text, review_value, create_date, is_active, category_id, type_id, upvotes, downvotes, product_url, external_id, spider_id,
                    review_metric_1_value, review_metric_2_value, review_metric_3_value, review_metric_4_value, category_review_metric_1_value, category_review_metric_2_value
                ) VALUES (
                    %s, %s, %s, %s, %s, 1, 'C001', 1, %s, %s, %s, %s, 2, %s, %s, %s, %s, %s, %s
                )
            """
            review_values = []
            for row in processed_file:
                try:
                    review_value = row['review_value']
                    if review_value is None:
                        logger.warning(f"Warning: Skipping review with null value: {row}")
                        continue
                    metrics = seed_rating_metrics(review_value)
                    review_values.append((
                        row['id'], row['product_id'], row['review_text'], review_value, row['date'],
                        row['upvotes'], row['downvotes'], row['product_url'], row['review_id'],
                        metrics[0], metrics[1], metrics[2], metrics[3], metrics[4], metrics[5]
                    ))
                except Exception as e:
                    logger.warning(f"Warning: Skipping Invalid Review: {row} with: {e}")

            if review_values:
                cursor.executemany(ingest_reviews_query, review_values)
                conn.commit()
                logger.info(f"Success: {len(review_values)} reviews ingested.")
            else:
                logger.info("Info: No valid reviews.")
    except Exception as e:
        logger.info(f"Failed: Reviews ingestion with: {e}")

def run(argv=None):
    from google.cloud import storage, secretmanager
    import mysql.connector

    project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
    bucket_name = os.getenv('RD_DF_RAW_DATA_BUCKET_NAME', 'rd-bckt-df-data-harvester')
    input_dir = os.getenv('INPUT_DIR')

    storage_client = storage.Client(project=project_id)
    secret_client = secretmanager.SecretManagerServiceClient()

    def rd_access_secret(secret_id: str, version_id: str = "latest") -> str:
        secret_name = f"projects/reviewdale-datafabric/secrets/{secret_id}/versions/{version_id}"
        response = secret_client.access_secret_version(request={"name": secret_name})
        return response.payload.data.decode("UTF-8")

    def write_temp_file(contents: str, suffix: str = ".pem") -> str:
        temp = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
        temp.write(contents.encode("utf-8"))
        temp.flush()
        return temp.name

    db_config = {
        "database": rd_access_secret("rd_db_stg_database"),
        "host": rd_access_secret("rd_db_stg_host"),
        "password": rd_access_secret("rd_db_stg_password"),
        "user": rd_access_secret("rd_db_stg_user"),
        "ssl_ca": write_temp_file(rd_access_secret("rd_db_stg_ssl_ca")),
        "ssl_cert": write_temp_file(rd_access_secret("rd_db_stg_ssl_cert")),
        "ssl_key": write_temp_file(rd_access_secret("rd_db_stg_ssl_key")),
    }

    conn_pool = mysql.connector.pooling.MySQLConnectionPool(
        pool_name="connection_pool",
        pool_size=5,
        pool_reset_session=True,
        **db_config
    )
    conn = conn_pool.get_connection()
    review_id_list = get_review_ids(conn)
    review_id_set = set(review_id_list)
    conn.close()

    if not input_dir:
        raise ValueError("Error: INPUT_DIR not set.")
    if not input_dir.endswith('/'):
        input_dir += '/'

    input_file_list = [blob.name for blob in storage_client.list_blobs(bucket_name, prefix=input_dir)]

    pipeline_options = PipelineOptions()
    pipeline_options.view_as(SetupOptions).save_main_session = True

    with beam.Pipeline(options=pipeline_options) as p:
        (
            p
            | 'Create input file list' >> beam.Create(input_file_list)
            | 'Process review files' >> beam.ParDo(ProcessReviewFile(review_id_set))
            | 'Log results' >> beam.Map(logger.info)
        )

if __name__ == '__main__':
    run()
