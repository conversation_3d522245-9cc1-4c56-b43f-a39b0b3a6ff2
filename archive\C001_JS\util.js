const cheerio = require('cheerio');
const puppeteer = require('puppeteer');
const path = require('path');
const FormData = require('form-data')
const { VM } = require('vm2');
const fs = require('fs')
const axios = require('axios')

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const getHomePageURL = (page) => {
    return `https://www.91mobiles.com/template/category_finder/finder_ajax.php?ord=0.3308491238223854&excludeId=&hash=&search=&hidFrmSubFlag=1&page=${page}&category=mobile&unique_sort=&gaCategory=PhoneFinder-filter&requestType=2&showPagination=1&listType=list&listType_v3=list&listType_v1=list&listType_v2=list&listType_v4=list&listType_v5=list&listType_v6=list&page_type=finder&finderRuleUrl=&selMobSort=recent&hdnCategory=mobile&user_search=&url_feat_rule=&buygaCat=finder-mob&isCustomAuto=0&hdndprice=&hdndprice1=&amount=0%3B65000&sCatName=mobile&price_range_apply=0&request_uri=/phonefinder.php`;
}

async function scrapeHomePageContent(link, pageObj) {
    try {
        await pageObj.goto(link, { waitUntil: 'domcontentloaded', timeout: 30000 });

        const content = await pageObj.content();

        return (content)
    } catch (err) {
        console.log('ERROR :: while scraping detail page links ', err.message);
        return false;
    }
}

const getDetailsPageLink = async (data) => {
    try {
        let $ = cheerio.load(data);
        let preContent = $("pre").text().trim();

        $ = cheerio.load(preContent);
        const links = [];

        $('a').each((index, element) => {
            let link = $(element).attr('href');
            if (link) {
                link = link.replace(/^\\+"|\\+"$/g, "").replace(/\\"/g, "").replace(/\\/g, "");
                links.push(`https://www.91mobiles.com${link}`);
            }
        });

        return links;
    } catch (error) {
        console.error("Error while fetching links:", error);
        return [];
    }
}


const extractProductAttributes = async (link, pageObj, appStorage, retryCount = 8) => {
    for (let attempt = 1; attempt <= retryCount; attempt++) {
        try {
            const attributes = [];
            await delay(Math.floor(Math.random() * (5000 - 1000) + 3000));

            await pageObj.goto(link, { waitUntil: 'domcontentloaded', timeout: 60000 });

            const dataLayer = await pageObj.evaluate(() => {
                return typeof dataLayer !== 'undefined' ? dataLayer : [];
            });

            if (!Array.isArray(dataLayer) || dataLayer.length === 0) {
                console.log("⚠️ dataLayer not found.");
            }

            // console.log("✅ Extracted dataLayer:", dataLayer);

            const content = await pageObj.content();

            const $ = cheerio.load(content);

            const phoneName = $('h1.h1_pro_head').text();
            const ratingText = $('.ratpt').text();
            const rating = parseFloat(ratingText.split(' ')[0]);

            const priceElement_1 = $('.store_prc');
            const priceElement_2 = $('.big_prc').eq(1);

            const priceText = priceElement_1.text().trim();

            const dataPrice = priceElement_2.attr('data-price') || priceText;

            const imageElements = $('img.alb_img.lazy[alt="Design"]');
            const imageDataSrcs = [];

            imageElements.each((index, element) => {
                const dataSrc = $(element).attr('data-src');
                const dataImageSrc = $(element).attr('data-imgsrc');
                if (dataSrc) {
                    imageDataSrcs.push(dataSrc);
                }
                if (dataImageSrc) {
                    imageDataSrcs.push(dataImageSrc)
                }
            });

            // const scriptContent = $('script[type="text/javascript"]').eq(3).html();

            let brand = '';
            let model = '';
            if (dataLayer) {
                // const vm = new VM({
                //     sandbox: { dataLayer: [] }
                // });

                // vm.run(scriptContent);

                // const dataLayer = vm.run('dataLayer');
                const productInfo = dataLayer.find(item => item.ProductModelName && item.Productbrand);

                if (productInfo) {
                    brand = productInfo.Productbrand;
                    model = productInfo.ProductModelName;
                } else {
                    console.log('Not found: Product information.');
                }
            } else {
                console.log('Not found: Script content in second script tag.');
            }
            let ratings = []
            $('.specMtrTxt.clr').each((index, element) => {
                const ratingsText = $(element).contents().not('span').text().trim();
                ratings.push(ratingsText)
            });

            const specs = [];
            $('.spec_box').each((index, element) => {
                const $specBox = $(element);
                const specItems = [];
                $specBox.find('.spec_table tr').each((idx, el) => {
                    const $row = $(el);
                    const title = $row.find('.spec_ttle').text().trim();
                    const description = $row.find('.spec_des').text().trim();
                    specItems.push({ title, description });
                });
                specs.push(specItems);
            });

            const productImage = $('img.overview_lrg_pic_img').attr('src');
            const imgElements = $('img.alb_img.lazy[alt="Design"]');
            const imagesDataSrcs = [];

            for (let imgCount = 0; imgCount < imgElements.length; imgCount++) {
                let element = imgElements[imgCount]
                const dataImgSrc = $(element).attr('data-src');
                const dataSrc = $(element).attr('data-imgsrc');

                if (dataImgSrc !== undefined && dataImgSrc !== null && dataImgSrc.length !== 0) {
                    imagesDataSrcs.push(dataImgSrc);
                }

                if (dataSrc !== undefined) {
                    imagesDataSrcs.push(dataSrc);
                }
            }

            $('div[tabpanel="Design"] img.alb_img.lazy-loaded').each((index, element) => {
                const imgDataSrcLazyLoaded = $(element).attr('data-imgsrc');
                if (imgDataSrcLazyLoaded) {
                    imagesDataSrcs.push(imgDataSrcLazyLoaded);
                }
            });
            // console.log(imageDataSrcs);

            let downloadedProductImages = await uploadToRDStorage(imagesDataSrcs, phoneName, appStorage);
            attributes.push({
                brand,
                downloadedProductImages,
                model,
                dataPrice,
                phoneName,
                productImage,
                rating,
                ratings,
                specs
            })
            return attributes;
        } catch (error) {
            console.log(`Attempt ${attempt} failed for ${link} `, error.message);
            if (attempt === retryCount) {
                await appendToFile('./not-found-products', link);
                return null;
            }
            await delay(4000);
            // await appendToFile('./not-found-products', link)
        }
    }
}

async function appendToFile(fileName, content) {
    const filePath = path.resolve(fileName);
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            fs.writeFile(filePath, content + ',', (err) => {
                if (err) throw err;
                console.log('File created and content written.');
            });
        } else {
            fs.appendFile(filePath, content + ',', (err) => {
                if (err) throw err;
                console.log('Content appended to file.');
            });
        }
    });
}

const getSecondaryProductImages = async (url) => {
    try {
        const productImageURLs = [];
        let url1 = sanitizeImageUrl(url);
        console.log("sanitized URL: ", url1);
        let i = 1
        while (true) {
            try {
                const newUrl = url1.replace(/-1\.jpg$/, `-${i}.jpg`);
                const res = await axios.get(newUrl);
                productImageURLs.push(newUrl)
                i++
            } catch (error) {
                if (error.response && error.response.status === 404) {
                    console.log("Received 404, breaking the loop.");
                    break;
                } else {
                    console.log("Error during request:", error.message);
                    break;
                }
            }
        }
        productImageURLs.pop();
        return productImageURLs
    } catch (error) {
        console.log("Error in getSecondaryProductImages:");
    }
};

const downloadImageToBase64 = async (url) => {
    try {
        const response = await axios.get(url, { responseType: 'arraybuffer' });
        return Buffer.from(response.data, 'binary').toString('base64');
    } catch (error) {
        console.log("Error : image to base64 ", error.message);
    }
};

const saveBase64Image = (base64Data, filePath) => {
    fs.writeFileSync(filePath, base64Data, { encoding: 'base64' });
};
const sanitizeImageUrl = (url) => {
    return url.split('?')[0];
};

const replaceSpacesWithHyphens = (inputString) => {
    let s = inputString.replace(/\s+/g, '-');
    if (s[0] === '-') {
        s = s.slice(1);
    }
    if (s[s.length - 1] === '-') {
        s = s.slice(0, s.length - 1);
    }
    return s
};

async function uploadBase64(base64String, bucketName, destFilePath, storage) {
    try {
        const buffer = Buffer.from(base64String, 'base64');
        const bucket = storage.bucket(bucketName);
        const file = bucket.file(destFilePath);

        await file.save(buffer, {
            metadata: { contentType: 'image/png' }
        });

        const publicUrl = `https://storage.googleapis.com/${bucketName}/${destFilePath}`;
        console.log(`Base64 file uploaded to ${publicUrl}`);
        return publicUrl;
    } catch (error) {
        console.error(`Error uploading Base64 file to ${bucketName}:`, error);
    }
}

// async function uploadToRDStorage(images, phoneName, appStorage) {
//     try {
//         const productImages = []
//         for (let imageCount = 0; imageCount < images.length; imageCount++) {
//             const imageURL = images[imageCount];
//             const sImageURL = sanitizeImageUrl(imageURL)
//             const imageBase64Data = await downloadImageToBase64(sImageURL);
//             let file = replaceSpacesWithHyphens(phoneName)
//             const fileName = `${file}_${imageCount + 1}.png`;
//             const imageBuffer = Buffer.from(imageBase64Data, 'base64');

//             // complete this function from here
//             const publicImageURL = await uploadBase64(imageBuffer, 'rd-bckt-asset', 'image/product/02', appStorage);
//             productImages.push(publicImageURL)
//         }

//         return productImages;
//     } catch (error) {
//         console.log("Error : in uploading image to bucket :", error.message);
//     }
// }

async function uploadToRDStorage(images, phoneName) {
    try {
        const productImages = []
        for (let imageCount = 0; imageCount < images.length; imageCount++) {
            const imageURL = images[imageCount];
            const sImageURL = sanitizeImageUrl(imageURL)
            const imageBase64Data = await downloadImageToBase64(sImageURL);
            let file = replaceSpacesWithHyphens(phoneName)
            const fileName = `${file}_${imageCount + 1}.png`;
            const imageBuffer = Buffer.from(imageBase64Data, 'base64');
            var mediaExchangeURL = process.env.SRVC_MEDIAEXCHANGE_URL;
            let form = new FormData();
            form.append("fileName", fileName);
            form.append("bucket", process.env.RD_MEDIA_BUCKET_NAME);
            form.append("folder", process.env.RD_MEDIA_BUCKET_FOLDER_PATH);
            form.append("file", imageBuffer, {
                filename: `${fileName}`,
                contentType: 'image/png',
            });
            const imageUploadResponse = await axios.post(mediaExchangeURL + `/upload-asset`, form, {
                headers: {
                    ...form.getHeaders(),
                },
            });
            productImages.push(imageUploadResponse.data.urls[0]);
        }

        return productImages;
    } catch (error) {
        console.log("Error : in uploading image to bucket :", error.message);
    }
}

module.exports = { getHomePageURL, scrapeHomePageContent, extractProductAttributes, getDetailsPageLink }
