export function extractObjectsFromNestedArrays(nestedArray) {
    const objectsArray = [];

    function recurseArray(element) {
        if (Array.isArray(element)) {
            for (const item of element) {
                recurseArray(item);
            }
        } else if (typeof element === 'object' && element !== null) {
            objectsArray.push(element);
        }
    }

    recurseArray(nestedArray);
    return objectsArray;
}