/**
 * Test script for GCP-Only Centralized Dataset functionality
 * This script tests the GCP-only implementation without local fallbacks
 */

import { fetchJsonFromGCP, fetchCentralizedProductDataset, getProductDataset } from './utils/util.js';

/**
 * Test the generic GCP JSON fetching function
 */
async function testGenericGCPFetch() {
    console.log('🧪 Testing Generic GCP JSON Fetch...\n');
    
    try {
        console.log('📡 Testing generic fetchJsonFromGCP function...');
        
        const productData = await fetchJsonFromGCP(
            'rd-bckt-source-data',
            'rd-entity/rd-products.json',
            'test product dataset'
        );
        
        console.log(`✅ Generic fetch successful`);
        console.log(`📊 Data type: ${Array.isArray(productData) ? 'Array' : typeof productData}`);
        console.log(`📊 Data size: ${Array.isArray(productData) ? productData.length : 'N/A'} items`);
        
        if (Array.isArray(productData) && productData.length > 0) {
            console.log(`📋 Sample item:`, JSON.stringify(productData[0], null, 2));
        }
        
        return productData;
        
    } catch (error) {
        console.log(`❌ Generic GCP fetch failed: ${error.message}`);
        return null;
    }
}

/**
 * Test the centralized product dataset fetching
 */
async function testCentralizedDatasetFetch() {
    console.log('\n🧪 Testing Centralized Product Dataset Fetch...\n');
    
    try {
        console.log('📡 Testing fetchCentralizedProductDataset function...');
        
        const products = await fetchCentralizedProductDataset();
        
        console.log(`✅ Centralized dataset fetch successful`);
        console.log(`📊 Products loaded: ${products.length}`);
        
        if (products.length > 0) {
            const sampleProduct = products[0];
            console.log(`📋 Sample product:`, JSON.stringify(sampleProduct, null, 2));
            
            // Validate expected fields
            const hasProductId = sampleProduct.product_id;
            const hasProductName = sampleProduct.product_name;
            
            console.log(`🔍 Data validation:`);
            console.log(`   Has product_id: ${hasProductId ? '✅' : '❌'}`);
            console.log(`   Has product_name: ${hasProductName ? '✅' : '❌'}`);
        }
        
        return products;
        
    } catch (error) {
        console.log(`❌ Centralized dataset fetch failed: ${error.message}`);
        return null;
    }
}

/**
 * Test the main getProductDataset function (GCP-only)
 */
async function testGetProductDataset() {
    console.log('\n🧪 Testing GCP-Only getProductDataset...\n');
    
    try {
        console.log('📡 Testing getProductDataset function (GCP-only)...');
        
        const products = await getProductDataset();
        
        console.log(`✅ getProductDataset successful`);
        console.log(`📊 Products loaded: ${products.length}`);
        console.log(`🌐 Confirmed GCP-only operation (no local fallback)`);
        
        return products;
        
    } catch (error) {
        console.log(`❌ getProductDataset failed: ${error.message}`);
        console.log(`🚫 No local fallback available in GCP-only mode`);
        return null;
    }
}

/**
 * Test product comparison logic with GCP data
 */
async function testProductComparison(products) {
    console.log('\n🧪 Testing Product Comparison Logic...\n');
    
    if (!products || products.length === 0) {
        console.log('❌ No products available for comparison testing');
        return;
    }
    
    // Import the comparison function
    const { matchProductName } = await import('./utils/util.js');
    
    // Test cases based on actual GCP dataset
    const testProducts = [
        'Apple iPhone 16 Pro Max',
        'Samsung Galaxy S25 Ultra', 
        'OnePlus 13 512GB',
        'Xiaomi 15 Ultra', // Likely new
        'Nothing Phone 3', // Likely new
    ];
    
    console.log(`📊 Testing comparison against ${products.length} GCP products...\n`);
    
    for (const testProductName of testProducts) {
        try {
            // Format products for comparison
            const formattedProducts = products.map(product => ({
                name: product.product_name || product.name || '',
                id: product.product_id || product.id || ''
            }));
            
            const matches = matchProductName(testProductName, formattedProducts);
            const exists = matches.length > 0;
            
            const statusIcon = exists ? '⏭️' : '🆕';
            const status = exists ? 'EXISTS' : 'NEW';
            
            console.log(`${statusIcon} ${testProductName}: ${status}`);
            
            if (exists && matches.length > 0) {
                console.log(`   Matched with: ${matches[0].name}`);
            }
            
        } catch (error) {
            console.log(`❌ Error testing ${testProductName}: ${error.message}`);
        }
    }
}

/**
 * Run all GCP-only tests
 */
async function runAllTests() {
    console.log('🚀 Starting GCP-Only System Tests...\n');
    console.log('🌐 Testing pure GCP Storage integration without local fallbacks\n');
    
    try {
        // Test 1: Generic GCP fetch
        const genericData = await testGenericGCPFetch();
        
        // Test 2: Centralized dataset fetch
        const centralizedData = await testCentralizedDatasetFetch();
        
        // Test 3: Main getProductDataset function
        const mainData = await testGetProductDataset();
        
        // Test 4: Product comparison (if we have data)
        const testData = mainData || centralizedData || genericData;
        if (testData) {
            await testProductComparison(testData);
        }
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 GCP-ONLY SYSTEM TEST SUMMARY');
        console.log('='.repeat(60));
        console.log(`🔧 Generic GCP fetch: ${genericData ? '✅ Working' : '❌ Failed'}`);
        console.log(`📋 Centralized dataset: ${centralizedData ? '✅ Working' : '❌ Failed'}`);
        console.log(`🌐 Main dataset function: ${mainData ? '✅ Working' : '❌ Failed'}`);
        console.log(`🚫 Local fallback: Disabled (GCP-only mode)`);
        console.log('='.repeat(60));
        
        if (mainData) {
            console.log('🎉 GCP-only system is working correctly!');
        } else {
            console.log('⚠️  GCP-only system requires proper authentication and connectivity.');
        }
        
    } catch (error) {
        console.log('\n❌ Test suite failed with error:', error.message);
        console.log('🌐 Ensure GCP authentication and network connectivity are properly configured.');
        process.exit(1);
    }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runAllTests();
}

export { testGenericGCPFetch, testCentralizedDatasetFetch, testGetProductDataset, testProductComparison };
