steps:
- name: 'gcr.io/cloud-builders/gcloud'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      echo "Listing contents of /workspace:"
      ls -la /workspace

- name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      echo "Retrieving private key from Secret Manager..."
      gcloud secrets versions access latest --secret=rd-df-vm-private-key > /workspace/vm-key.pem
      chmod 600 /workspace/vm-key.pem

- name: 'gcr.io/cloud-builders/gcloud'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      echo "Copying files to VM..."
      for file in /workspace/*; do
        if [ -f "$file" ] || [ -d "$file" ]; then
          gcloud compute scp --recurse "$file" root@rd-vm-df-pipelines:/home/<USER>/rd_df_data_harvester --zone asia-south1-c
        fi
      done

timeout: '1800s'
