require('dotenv').config()
const { getHomePageURL, scrapeHomePageContent, detailPageLinks, getDetailsPageLink, extractProductAttributes } = require('./util');
const fs = require('fs');
const { Storage } = require('@google-cloud/storage');
const path = require('path');
const FormData = require("form-data");
const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');

const dfStorage = new Storage({ projectId: 'reviewdale-datafabric' });
const appStorage = new Storage({ projectId: 'reviewdale' });
puppeteer.use(StealthPlugin());

async function startIngester() {
    let totalProducts = 0;
    let batchCount = 24;
    const BATCH_COUNT = 1630;

    const browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox', '--disable-setuid-sandbox'], protocolTimeout: 120000 });
    const page = await browser.newPage();
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
    await page.setViewport({ width: 1280, height: 800 });
    await page.setJavaScriptEnabled(true);


    while (batchCount <= BATCH_COUNT) {
        let rawBatchData = [];
        let mainPageHTML = await scrapeHomePageContent(getHomePageURL(batchCount), page);
        if (mainPageHTML === false) {
            break;
        }
        let detailPageLinks = await getDetailsPageLink(mainPageHTML);

        const details = await Promise.all(detailPageLinks.map(async link => {
            let attributes = await extractProductAttributes(link, page, appStorage);
            return attributes;
        }));
        rawBatchData.push(...details);

        totalProducts += rawBatchData.length
        await exportRawData(`./C001/C001-raw-data-${batchCount}.json`, rawBatchData);
        const p = path.join(__dirname, 'C001', `C001-raw-data-${batchCount}.json`);
        await uploadToRDStorage(p)
        batchCount += 1;
        rawBatchData = []
    }

    await browser.close();
}

async function exportRawData(filePath, data) {
    const rawData = JSON.stringify(data, null, 2);

    return fs.promises.writeFile(filePath, rawData)
        .then(() => {
            console.log("Data export successfully : ", filePath);
        })
        .catch(error => {
            console.error(`Error writing to ${filePath}`, error);
            throw error;
        });
}

async function uploadToRDStorage(filePath) {
    try {
        const bucketName = process.env.RD_RAW_DATA_BUCKET;
        const fileName = path.basename(filePath);
        const destinationPath = `C001/product/02/${fileName}`;

        await dfStorage.bucket(bucketName).upload(filePath, {
            destination: destinationPath,
        });

        console.log(`✅ File uploaded successfully to: gs://${bucketName}/${destinationPath}`);
    } catch (error) {
        console.error('❌ Error uploading file:', error);
    }
}

startIngester()