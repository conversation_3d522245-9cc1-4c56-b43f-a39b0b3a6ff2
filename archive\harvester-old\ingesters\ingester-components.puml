@startuml
package "rd DH Ingester" as ScraperSystem {
    component "Raw File" as RawFile
    component "Source Site" as SourceSite
    component "rd Cache DB" as rdCacheDB
    component "rd Storage" as rdCloudStorage

    [Start] --> GetNextProduct
    GetNextProduct --> CheckTokenValidity
    rdCacheDB --> GetNextProduct
    CheckTokenValidity --> ExtractHTML : Fetch new token
    SourceSite --> ExtractHTML : Request HTML
    ExtractHTML --> RawFile : Extract Attributes
    RawFile --> rdCloudStorage
    ExtractHTML --> rdCacheDB : Mark product as scraped
    rdCloudStorage --> [End]: If no product remaining
}

@enduml