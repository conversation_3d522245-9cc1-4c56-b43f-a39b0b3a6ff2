import chalk from "chalk";
import cheerio from "Cheerio";
import { fetchReviewPageContent } from "./fetchLinks.js";

const BASE_URL = "https://www.flipkart.com"
export const extractDetailPageLink = async (content) => {
    try {
        const $ = cheerio.load(content);
        const productDetailLink = $('.cPHDOP.col-12-12');
        const links = [];
        productDetailLink.each((index, item) => {
            const href = $(item).find('a.CGtC98').attr('href');
            if (href !== undefined) {
                links.push(href);
            }
        });
        return BASE_URL + links[0]
    } catch (error) {
        console.log(chalk.red("Error in extracting detail page link: ", error));
    }
}

export const detailsReviewPageURL = async (content) => {
    try {
        const $ = cheerio.load(content);
        const parentDiv = $('.col.pPAw9M').find('a').attr('href');
        return BASE_URL + parentDiv
    } catch (error) {
        console.log(chalk.red("Error in detailsReviewPageURL: ", error));
    }
}

export const extractReviews = async (url) => {
    try {
        const pageContent = await fetchReviewPageContent(url);
        const $ = cheerio.load(pageContent);
        const data = [];
        const elements = $('.col.EPCmJX.Ma1fCG');

        elements.each((index, element) => {
            const obj = {};
            obj.dislikes = $(element).find('.qhmk-f ._6kK6mk.aQymJL span.tl9VpF').text().trim();
            obj.likes = $(element).find('.qhmk-f ._6kK6mk span.tl9VpF').first().text().trim();
            obj.reviewBody = $(element).find('.ZmyHeo div div').first().text().trim();
            obj.reviewDate = $(element).find('p._2NsDsF').last().text().trim();
            obj.reviewerName = $(element).find('p._2NsDsF.AwS1CA').text().trim();
            obj.reviewTitle = $(element).find('p.z9E0IG').text().trim();
            obj.reviewValue = $(element).find('.XQDdHH.Ga3i8K').text().trim();
            data.push(obj);
        });
    } catch (error) {
        console.log(chalk.red("Error in extractReviews: ", error));
    }
}