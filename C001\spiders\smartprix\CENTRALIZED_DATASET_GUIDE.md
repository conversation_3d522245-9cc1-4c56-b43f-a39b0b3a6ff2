# Centralized Product Dataset Integration Guide

## 🎯 Overview

The New Product Discovery feature has been enhanced to use a **centralized product dataset** stored in Google Cloud Storage instead of local `PRODUCTS` arrays. This ensures all spiders across the system use the same unified product database for consistent duplicate detection.

## 🏗️ Architecture Changes

### **Before (Local Dataset)**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Spider A      │    │   Spider B      │    │   Spider C      │
│                 │    │                 │    │                 │
│ settings.js     │    │ settings.js     │    │ settings.js     │
│ PRODUCTS[19k]   │    │ PRODUCTS[15k]   │    │ PRODUCTS[22k]   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **After (Centralized Dataset)**
```
                    ┌─────────────────────────────┐
                    │     Google Cloud Storage    │
                    │  rd-bckt-source-data        │
                    │  rd-entity/rd-products.json │
                    │  📊 Centralized Dataset     │
                    └─────────────────────────────┘
                                   │
                    ┌──────────────┼──────────────┐
                    │              │              │
            ┌───────▼──────┐ ┌─────▼──────┐ ┌────▼───────┐
            │   Spider A   │ │  Spider B  │ │  Spider C  │
            │              │ │            │ │            │
            │ getProduct   │ │ getProduct │ │ getProduct │
            │ Dataset()    │ │ Dataset()  │ │ Dataset()  │
            └──────────────┘ └────────────┘ └────────────┘
```

## 🔧 Implementation Details

### **1. Centralized Dataset Location**
- **Bucket**: `rd-bckt-source-data`
- **Path**: `rd-entity/rd-products.json`
- **Format**: JSON array of product objects

### **2. Expected Data Structure**
```json
[
  {
    "id": "P1234567890",
    "product_name": "Samsung Galaxy S24 Ultra",
    "product_id": "P1234567890",
    // ... other fields
  },
  {
    "id": "P1234567891", 
    "product_name": "iPhone 15 Pro Max",
    "product_id": "P1234567891",
    // ... other fields
  }
]
```

### **3. Key Functions Added**

#### `fetchCentralizedProductDataset(forceRefresh = false)`
- Fetches product dataset from GCP Storage
- Implements 30-minute caching to avoid repeated downloads
- Returns array of products from centralized dataset

#### `getProductDataset(forceRefresh = false)`
- Primary function for getting product dataset
- Tries centralized dataset first
- Falls back to local `PRODUCTS` array if GCP unavailable
- Ensures system continues working even if GCP is down

### **4. Caching Strategy**
- **Cache Duration**: 30 minutes
- **Cache Location**: In-memory (per process)
- **Force Refresh**: Available via `forceRefresh` parameter
- **Fallback**: Uses cached data if GCP fetch fails

## 🚀 Usage

### **Basic Usage**
```javascript
import { getProductDataset } from './utils/util.js';

// Get product dataset (centralized + fallback)
const products = await getProductDataset();
console.log(`Loaded ${products.length} products`);
```

### **Force Refresh**
```javascript
// Force refresh from GCP (bypass cache)
const products = await getProductDataset(true);
```

### **Direct Centralized Access**
```javascript
import { fetchCentralizedProductDataset } from './utils/util.js';

// Direct access to centralized dataset only
const centralizedProducts = await fetchCentralizedProductDataset();
```

## 🧪 Testing

### **Test Centralized Dataset**
```bash
node test-centralized-dataset.js
```

This will test:
- ✅ GCP dataset fetching
- ✅ Fallback mechanism
- ✅ Product comparison logic
- ✅ Caching functionality

### **Run New Product Discovery**
```bash
node main.js
```

Expected output:
```
🚀 Starting New Product Discovery Process...
📊 Loading centralized product dataset...
🔄 Fetching centralized product dataset from GCP...
✅ Successfully loaded 25847 products from centralized dataset
📦 Batch 1: Total products in DOM = 24
🔍 Processing 24 products from batch 1...
⏭️  SKIPPED (exists): Samsung Galaxy S24 Ultra
🆕 NEW PRODUCT FOUND: Xiaomi 14 Ultra Global
✅ NEW PRODUCT SAVED: Xiaomi 14 Ultra Global (ID: P1703123456789)
```

## 🔐 Authentication

The system uses the existing GCP authentication setup:
- **Project ID**: `reviewdale-datafabric`
- **Authentication**: Default Application Credentials
- **Storage Client**: `@google-cloud/storage`

## 📊 Benefits

### **1. Consistency**
- All spiders use the same product dataset
- No more version mismatches between different spider instances
- Unified duplicate detection across the entire system

### **2. Scalability**
- Centralized updates benefit all spiders immediately
- No need to manually sync product lists across multiple spiders
- Single source of truth for product data

### **3. Reliability**
- Fallback mechanism ensures system continues working
- Caching reduces GCP API calls and improves performance
- Error handling prevents system failures

### **4. Maintenance**
- Single location to update product dataset
- Easier to add new products discovered by any spider
- Simplified data management workflow

## 🛠️ Configuration

### **Environment Variables**
No additional environment variables needed. Uses existing GCP setup:
- `GOOGLE_APPLICATION_CREDENTIALS` (if using service account file)
- Or default application credentials

### **Cache Settings**
```javascript
const CACHE_DURATION_MS = 30 * 60 * 1000; // 30 minutes
```

## 🔍 Monitoring

### **Success Indicators**
```
✅ Successfully loaded 25847 products from centralized dataset
📋 Using cached centralized product dataset
```

### **Fallback Indicators**
```
⚠️  Centralized dataset unavailable: [error message]
📋 Using local PRODUCTS array as fallback (19220 products)
```

### **Error Indicators**
```
❌ Error fetching centralized product dataset: [error message]
❌ Both centralized and local product datasets are unavailable
```

## 🚨 Troubleshooting

### **Issue**: "Centralized product dataset file not found"
**Solution**: Verify the file exists at `rd-bckt-source-data/rd-entity/rd-products.json`

### **Issue**: "Invalid product dataset format"
**Solution**: Ensure the GCP file contains a valid JSON array

### **Issue**: "Both centralized and local datasets unavailable"
**Solution**: 
1. Check GCP authentication
2. Verify network connectivity
3. Ensure local `settings.js` has `PRODUCTS` array

### **Issue**: Slow performance
**Solution**: 
1. Check if caching is working (should see "Using cached" messages)
2. Verify GCP bucket region and network latency
3. Consider increasing cache duration if needed

## 📈 Performance Metrics

- **First fetch**: ~2-5 seconds (depending on dataset size)
- **Cached fetch**: ~1-10ms
- **Fallback time**: ~100-500ms
- **Memory usage**: ~50-100MB for 25k products

## 🔄 Migration Path

1. **Phase 1**: Deploy centralized dataset integration (✅ Complete)
2. **Phase 2**: Test with existing spiders
3. **Phase 3**: Update centralized dataset with latest products
4. **Phase 4**: Remove local `PRODUCTS` arrays (optional)

The system is designed to work immediately with existing setups while providing the benefits of centralization.
