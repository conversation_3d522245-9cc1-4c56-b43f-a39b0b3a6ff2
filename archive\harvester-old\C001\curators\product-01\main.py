from google.cloud import storage
from google.oauth2 import service_account
import json
from utils.util import identify_attributes
from data_pump.data_pump import ingest_product_data
import os
from collections import OrderedDict

rd_bucket_name = os.getenv('RD_DF_RAW_DATA_BUCKET_NAME')
phone_price_dict = OrderedDict()

def read_files_from_rd_bucket(folder_name):
    rd_storage_client = storage.Client()
    raw_data_files = rd_storage_client.list_blobs(rd_bucket_name, prefix=folder_name)
    return raw_data_files

def read_file_content(file_name):
    rd_storage_client = storage.Client()
    rd_bucket = rd_storage_client.bucket(rd_bucket_name)
    raw_content = rd_bucket.blob(file_name)
    raw_data = raw_content.download_as_text()
    return raw_data

def read_file_from_rd_bucket(bucket_name, file_name, service_account_key_path):
    credentials = service_account.Credentials.from_service_account_file(service_account_key_path)
    rd_client = storage.Client(credentials=credentials)
    rd_bucket = rd_client.get_bucket(bucket_name)
    file_blob = rd_bucket.blob(file_name)
    file_content = file_blob.download_as_text()
    return file_content

def start_curator():
        rd_bucket_name = os.getenv('RD_DF_RAW_DATA_BUCKET_NAME')
        batches = 1629
        rem = [3,6,7,9,16,23]
        for batch in range(791, batches+1):
        # for a in range(len(rem)):
        #     batch = rem[a]
            try:    
                file_name = f'C001/C0001-raw-data-{batch}.json'
                service_account_key_path = './rd-df-service-key.json'

                raw_content = read_file_from_rd_bucket(rd_bucket_name, file_name, service_account_key_path)
                converted_data_object = json.loads(raw_content)
                curated_data = identify_attributes(converted_data_object, phone_price_dict)
                ingest_product_data(curated_data)
                print(f"Curated data ingested for batch: f'C001/C0001-raw-data-{batch}.json'")
                print('---')
        
            except json.JSONDecodeError as e:
                print(f"Error decoding JSON from raw_file f'C001/C0001-raw-data-{batch}.json': {e}")
            except Exception as e:
                print(f"Error processing raw_file f'C001/C0001-raw-data-{batch}.json': {e}")

def load_additional_attribute_file():
    for raw_file in read_files_from_rd_bucket('addidional_attribute'):
        try:
            content = read_file_content(raw_file.name)
            converted_data_object = json.loads(content)
            for item_list in converted_data_object:
                if item_list is not None:  
                    for item in item_list:
                        if item is not None:  
                            data_price = item[0]['dataPrice']
                            phone_name = item[0]['phoneName']
                            product_id = item[0]['productId']
                            data_price = data_price if data_price is not None else 'Unknown'
                            phone_name = phone_name if phone_name is not None else 'Unknown'
                            product_id = product_id if product_id is not None else 'Unknown'
                            
                            phone_price_dict[phone_name] = {
                                'dataPrice': data_price,
                                'productId': product_id
                            }
            start_curator()
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON from raw_file {raw_file.name}: {e}")
        except Exception as e:
            print(f"Error processing raw_file {raw_file.name}: {e}")

load_additional_attribute_file()
