from utils.curation_utils import (get_colors, get_weight, get_ram, get_internal_memory, 
                                  extract_bt_version, get_dimensions, get_usb, 
                                  get_resolution_refresh_rate, get_camera_descriptions, 
                                  extract_inches, extract_supported_network_types, 
                                  extract_wifi, convert_date, generate_product_id, generate_brand_id, curate_product_price, curate_battery_string, curate_memory, extract_memory_unit, convert_memory_to_gb)

from utils.unit_conversion import (convert_mm_to_cm, convert_inches_to_meters)

from data_pump.data_pump import get_brand_id, ingest_brand
import logging

logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')

def clean_string(s):
    return s.replace("\n", "").strip()

def identify_attributes(raw_data, phone_price_dict):
    products = []
    try:
        if not raw_data:
            logging.error("raw_data is None or empty")
            return products

        for phone_group in raw_data:
            if not phone_group:
                continue

            for phone in phone_group:
                print(clean_string(phone.get('phoneName')), len(clean_string(phone.get('phoneName'))))
                phone_price_data = phone_price_dict.get(clean_string(phone.get('phoneName')), {})
                print("Price data: ", phone_price_data)
                if not phone_price_data:
                    print("into price not foun block")
                    logging.info(f"phone_price_data is None for phoneName: {phone.get('phoneName', 'Unknown')}")
                    phone_price_data['dataPrice'] = ""
                    phone_price_data['productId'] = ""

                attributes = {}
                camera = []
                curated_data = {}

                curated_data['price'] = curate_product_price(phone_price_data['dataPrice'])
                curated_data['external_id'] = phone_price_data['productId']
                curated_data['product_value'] = phone.get('rating', '')
                colors = ''
                dimensions = []
                display = ''
                has_fingerprint = False
                has_GPS = False
                has_NFC = False
                has_wireless_charging = False
                internal_memory_unit = None
                ram_unit = None
                resolution = []
                sensors = ''
                usb = []
                wifi  = ''
                weight = ''

                split_phonename = phone.get('phoneName', '').split()

            curated_data['brand'] = split_phonename[0] if len(split_phonename) > 0 else None
            curated_data['model'] = split_phonename[1] if len(split_phonename) > 1 else None
            curated_data['series'] = split_phonename[2] if len(split_phonename) > 2 else None
            curated_data['variant'] = " ".join(split_phonename[3:]) if len(split_phonename) > 3 else None

            is_brand_present = get_brand_id(curated_data['brand'])
            if not is_brand_present:
                curated_data['brand_id'] = generate_brand_id()
                ingest_brand(curated_data['brand_id'], curated_data['brand'])
            else:
                curated_data['brand_id'] = is_brand_present[0]


                curated_data['product_id'] = generate_product_id('C001', curated_data['brand_id'])

                if not phone.get("specs"):
                    logging.error(f"phone specs are missing for phoneName: {phone.get('phoneName', 'Unknown')}")
                    continue

                for spec_category in phone["specs"]:
                    if not spec_category:
                        continue
                    for spec in spec_category:
                        title = spec.get('title')
                        description = spec.get('description', '')
                        if title == "Launch Date":
                            attributes['attribute_1'] = { "value": convert_date(description) }
                        elif title == "Colours":
                            colors = get_colors(description)
                            colors = ', '.join(colors)
                        elif title in ["Height", "Width", "Thickness"]:
                            dimensions.append(spec)
                        elif title == "Weight":
                            weight = get_weight([spec])
                        elif title == "Battery":
                            attributes['attribute_7'] = {"value" : curate_battery_string(description), "phy-qty-id": 3}
                        elif title in ["Rear Camera", "Front Camera"]:
                            camera.append(spec)
                        elif title == "Display":
                            display = extract_inches(description)
                        elif title == "RAM":
                            raw_ram_value = curate_memory(get_ram(description))
                            ram_unit = extract_memory_unit(get_ram(description))
                            attributes['attribute_10'] = { "value": convert_memory_to_gb(raw_ram_value, ram_unit), "phy-qty-id": 1 }
                        elif title == "Internal Memory":
                            raw_memory_value = curate_memory(get_internal_memory(description))
                            internal_memory_unit = extract_memory_unit(get_internal_memory(description))
                            attributes['attribute_11'] = { "value": convert_memory_to_gb(raw_memory_value, internal_memory_unit), "phy-qty-id": 1 }
                        elif title == "Operating System":
                            attributes['attribute_12'] = { "value": description }
                        elif title == "Bluetooth":
                            attributes['attribute_13'] = { "value": extract_bt_version(description) }
                        elif title == "CPU":
                            attributes['attribute_14'] = { "value": description }
                        elif title == "Refresh Rate":
                            resolution.append(spec)
                        elif title in ["USB Type-C", "USB Connectivity"]:
                            usb.append(spec)
                        elif title == "Fingerprint Sensor":
                            has_fingerprint = True
                        elif title == "Network Support":
                            result = extract_supported_network_types([description])
                            network_types = ','.join(g for gens in result for g in gens)
                            attributes['attribute_18'] = { "value": network_types }
                        elif title == "Wireless Charging":
                            has_wireless_charging = True
                        elif title == "GPS":
                            has_GPS = True
                        elif title == "NFC":
                            has_NFC = True
                        elif title == "Wi-Fi":
                            wifi  = description
                        elif title == "Other Sensors":
                            sensors = description

                attributes['attribute_19'] = { "value": "Yes" if has_wireless_charging else "No" }
                attributes['attribute_20'] = { "value": "Yes" if has_fingerprint else "No" }
                attributes['attribute_21'] = { "value": "Yes" if has_GPS else "No" }
                attributes['attribute_22'] = { "value": "Yes" if has_NFC else "No" }
                attributes['attribute_23'] = { "value": extract_wifi([wifi])[0] if wifi else "" }
                attributes['attribute_24'] = { "value": sensors if sensors else "" }
                attributes['attribute_5'] = { "value": weight if weight else "", "phy-qty-id": 2 }
                attributes['attribute_3'] = { "value": colors if colors else "", "phy-qty-id": 1 }
                attributes['attribute_2'] = { "value": '1'}
                dimensions = get_dimensions(dimensions)

                attributes['attribute_4_1'] = convert_mm_to_cm(dimensions[0]) if dimensions[0] else None
                attributes['attribute_4_2'] = convert_mm_to_cm(dimensions[1]) if dimensions[1] else None
                attributes['attribute_4_3'] = convert_mm_to_cm(dimensions[2]) if dimensions[2] else None
                attributes['attribute_4'] = {
                    "value" : {
                        "length": attributes['attribute_4_1'],
                        "width": attributes['attribute_4_2'],
                        "depth": attributes['attribute_4_3'],
                    },
                    "phy-qty-id" : 8
                }

                attributes['attribute_9'] = { "value": convert_inches_to_meters(display) if display else None, "phy-qty-id": 8 }
                attributes['attribute_8'] = { "value": get_camera_descriptions(camera), "phy-qty-id": 12 }
                attributes['attribute_15'] = None
                attributes['attribute_16'] = { "value": get_resolution_refresh_rate(resolution), "phy-qty-id": 7 }
                attributes['attribute_17'] = { "value": get_usb(usb) }

                for attribute_count in range(1, 24):
                    attribute_key = f'attribute_{attribute_count}'
                    if attribute_key not in attributes:
                        attributes[attribute_key] = ""
                        
                sorted_attributes = dict(sorted(attributes.items(), key=lambda item: int(item[0].replace('attribute_', ''))))

                for attribute, value in sorted_attributes.items():
                    curated_data[attribute] = value
                curated_data['product_images'] = phone.get('downloadedProductImages', [])
                products.append(curated_data)

        return products
    except Exception as e:
        logging.error(f"Error while curation: {e}", exc_info=True)