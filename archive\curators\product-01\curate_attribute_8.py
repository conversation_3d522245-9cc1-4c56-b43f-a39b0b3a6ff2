import json
from data_pump.rd_db_config import get_connection_from_pool, connection_pool  # type: ignore

def extract_first_number(value):
    try:
        first_part = str(value).split('+')[0].strip()
        return int(''.join(filter(str.isdigit, first_part)))
    except Exception:
        return None
    
def update_attribute_8(connection):
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT id, attribute_8 FROM vw_product WHERE ingester_id = '02'
            """)
            products = cursor.fetchall()

            for product in products:
                product_id = product[0]
                attribute_8 = product[1]

                if not attribute_8:
                    print(f"Skipping product_id {product_id} as attribute_8 is None or empty.")
                    continue

                try:
                    attribute_8_json = json.loads(attribute_8)
                except json.JSONDecodeError:
                    print(f"Error decoding JSON for product_id {product_id}")
                    continue

                # Ensure 'value' is a dictionary
                value_data = attribute_8_json.get('value')
                if not isinstance(value_data, dict):
                    print(f"Invalid 'value' structure for product_id {product_id}")
                    value_data = {}

                value_primary = extract_first_number(value_data.get('primary'))
                value_secondary = extract_first_number(value_data.get('secondary'))

                try:
                    if value_primary is not None:
                        value_primary = int(''.join(filter(str.isdigit, str(value_primary))))
                except Exception as e:
                    print(f"Error parsing primary value for product_id {product_id}: {e}")
                    value_primary = None

                try:
                    if value_secondary is not None:
                        value_secondary = int(''.join(filter(str.isdigit, str(value_secondary))))
                except Exception as e:
                    print(f"Error parsing secondary value for product_id {product_id}: {e}")
                    value_secondary = None

                phy_qty_id = attribute_8_json.get('phy-qty-id', None)

                updated_attribute_8 = {
                    'value': {
                        'primary': value_primary,
                        'secondary': value_secondary
                    },
                    'phy-qty-id': phy_qty_id
                }

                cursor.execute("""
                    UPDATE vw_product
                    SET attribute_8 = %s
                    WHERE id = %s
                """, (json.dumps(updated_attribute_8), product_id))
                print(f"Updated product_id {product_id} with new attribute_8.")

            connection.commit()

    except Exception as e:
        print(f"Error in update_attribute_8: {e}")
        connection.rollback()


connection = get_connection_from_pool(connection_pool)
if connection is None:
    raise Exception("Unable to get DB connection from pool.")

update_attribute_8(connection)
