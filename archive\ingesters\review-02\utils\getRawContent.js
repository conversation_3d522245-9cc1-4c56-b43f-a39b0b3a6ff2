import cheerio from 'cheerio';

export const getMainProductPageContent = async (axiosClient, productUrl) => {
    try {
        const rawContent = await axiosClient.get(productUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
                'Cookie': `aws-ubid-main=401-2064847-5602516; aws-target-data=%7B%22support%22%3A%221%22%7D; regStatus=registered; aws-session-id=614-6615403-9762551; aws-analysis-id=614-6615403-9762551; aws-session-id-time=1709202783l; AMCV_7742037254C95E840A4C98A6%40AdobeOrg=1585540135%7CMCIDTS%7C19838%7CMCMID%7C35333270114292691563431850076360586520%7CMCAAMLH-1714540555%7C12%7CMCAAMB-1714540555%7CRKhpRz8krg2tLO6pguXWp5olkAcUniQYPHaMWWgdJ3xzPWQmdj0y%7CMCOPTOUT-1713942955s%7CNONE%7CMCAID%7CNONE%7CvVersion%7C4.4.0; aws-target-visitor-id=1704188500322-576144.48_0; aws-userInfo-signed=**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; aws-userInfo=%7B%22arn%22%3A%22arn%3Aaws%3Aiam%3A%3A543335767029%3Aroot%22%2C%22alias%22%3A%22%22%2C%22username%22%3A%22Vaishnavi%2520Kamekar%22%2C%22keybase%22%3A%22K%2Bo9%2BCwTlcxyMEzWFc3zJ0sokv%2Fv4R1TltbY%2FNHaVPs%5Cu003d%22%2C%22issuer%22%3A%22http%3A%2F%2Fsignin.aws.amazon.com%2Fsignin%22%2C%22signinType%22%3A%22PUBLIC%22%7D; session-id=135-5098681-4049945; sp-cdn="L5Z9:IN"; ubid-main=134-5248783-4913338; lc-main=en_US; mbox=session#********************************#1721444562|PC#********************************.41_0#1784687502; AMCV_4A8581745834114C0A495E2B%40AdobeOrg=179643557%7CMCIDTS%7C19925%7CMCMID%7C35724516899764334233403422408950677683%7CMCAAMLH-1722047502%7C12%7CMCAAMB-1722047502%7CRKhpRz8krg2tLO6pguXWp5olkAcUniQYPHaMWWgdJ3xzPWQmdj0y%7CMCOPTOUT-1721449902s%7CNONE%7CMCAID%7C33449529D62F00B8-40000DFD22825D3B%7CvVersion%7C5.5.0; _mkto_trk=id:365-EFI-026&token:_mch-aws.amazon.com-1704188499169-17375; s_nr=1721442707376-New; s_lv=1721442707377; x-main="Bg@eNv4P@gyYHvA1b5?CMpvQDBvqi@@vEU6jfN?fRK5z8LLbkbH4iPO4FXP8ZLrJ"; at-main=Atza|IwEBILveghCsfKGbAtM3Hvc4xc1aYn3lKAdUhRAPVCjR9NLeyRNBrfOHwAw5c0lq62qcgZbtN88y9ipJeEiq-6-TLWhQ2Vnc0Z_b8agR99Kx-IN74bOb6YoRlXm03p0sMtodmMeXs-PhjtKSbKUThsQbQvMawr_QwKfj4cfD7uMOrCi_s8UAC0OuJ1I18fQE3TGS4cD_81PR8qBxypo8boDS8r4omf_pJFG69i8zzCsVRaFJ0w; sess-at-main="BRikk28Bc/32qeqxT0dToMb3d4nmAhyQYWcpJTYlhpc="; sst-main=Sst1|PQGDzI5ehSrZYzSWvx4uHB5UCt-wffXblltNT7McMOR063VQvVY4QGOQUhr3J9tY4zCzHxEF0hAqyfFPq04JpsMnJo_1l5Z36fJsrNFO2_1R_mQcz_p5kxAQarjHA3WNgz5yuNABcvJdq3JT6Kyy6Cl6iLtV8Qu2S5mjF7AN6FroqYZtCq0UqaQHTL8brlwtpuqfOmWhCiRZrnJJOqaMM3prTQRKmAmpdW7ImWo8HnKw9j0Z40_4NWL0MnjD4hpUfgp2C0yLqPfCFn6SSwxc6IBn3TYfYa_wp1Y8ctJwKOBviwilnSAyYvQr64Ebyxh6Y9FR; session-id-time=2082787201l; i18n-prefs=INR; session-token=B6eIz0sGHAA0CzQS+59L8Yi2CwyAu5/hknMEDeh86PWvjeSxCzzPLZfbQIDlImfHvCasORt9qqzBUCCqtiL4VrF/it3uzHtODbDW38GJMsXAFZUwp1l0vfcGdzaMkEmH9a6pAVeoiN+TRXI7Ms+PSeuB7brMMFoFqra4XJCGBhVKBGAbmN4Zl6vNHPmlbCHaA0rjdF2bshReriIot/iO3S1F5/dbvalC2Gf/yQwZ+aARi2YyNFDEeQsGIaCOKNfkRoiaUITloHgkVLv+03jz025UPlw2pThefNLtznjU7k6pir1oGRHOISTSDCEnuH6elZfr6x03zaw26SJA/cJQkua/mlVPLqztxmFR31Rw8+WiRZDtdkXP8QmBhX+nZUo3; csm-hit=tb:s-99Q3BBX5XAP9JGWCVMD1|1723967015434&t:1723967015677&adb:adblk_yes`,
                'Connection': 'keep-alive'
            }
        });

        return rawContent.data;
    } catch (error) {
        console.log("Error while getting product content : ", error);
    }
}

export const getNextReviewPageLink = async (html) => {
    try {
        const $ = cheerio.load(html);
        const href = $('li.a-last a').attr('href');
        return href;
    } catch (error) {
        console.log(error.message);
    }
}
