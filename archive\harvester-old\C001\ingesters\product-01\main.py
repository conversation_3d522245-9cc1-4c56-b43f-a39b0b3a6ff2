import os
import json
import asyncio
import aiohttp
from dotenv import load_dotenv
from util import get_home_page_url, scrape_home_page_content, get_details_page_links, extract_product_attributes, upload_to_rd_storage, export_raw_data

load_dotenv()

AXIOS_TIMEOUT = int(os.getenv('AXIOS_TIMEOUT'))
SRVC_MEDIAEXCHANGE_URL = os.getenv('SRVC_MEDIAEXCHANGE_URL')
RD_BUCKET_FOLDER_PATH = os.getenv('RD_BUCKET_FOLDER_PATH')
RD_RAW_DATA_BUCKET = os.getenv('RD_RAW_DATA_BUCKET')

async def start_ingester():
    total_products = 0
    batch_count = 1
    TOTAL_BATCHES = 1

    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=AXIOS_TIMEOUT/1000)) as client:
        while batch_count <= TOTAL_BATCHES:
            raw_batch_data = []
            main_page_html = await scrape_home_page_content(get_home_page_url(batch_count), client, batch_count)
            if main_page_html is False:
                break
            detail_page_links = await get_details_page_links(main_page_html)
            for link in range(len(detail_page_links)):
                a = await asyncio.gather(extract_product_attributes(detail_page_links[link], client))
                raw_batch_data.extend(a)

            await export_raw_data(f'./C001/C001-raw-data-{batch_count}.json', raw_batch_data)
                        
            total_products += len(raw_batch_data)
            await upload_to_rd_storage(f'C001/C001-raw-data-{batch_count}.json', batch_count)
            batch_count += 1
            raw_batch_data = []

asyncio.run(start_ingester())
