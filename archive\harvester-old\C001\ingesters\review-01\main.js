import chalk from "chalk";
import dotenv from 'dotenv';
import { searchProduct, fetchDetailPageContent } from "./fetchLinks.js";
import { exportRawData } from "./fsUtil.js";
import { detailsReviewPageURL, extractDetailPageLink, extractReviews } from "./extractAttributes.js";
dotenv.config();

async function main() {
    try {
        // Step 0: Get product names in list
        // Step 1: Search for product
        // Step 2: Fetch product detail link
        // Step 3: Get detail page content 
        // Step 4: Fetch more reviews link
        // Step 5: Get reviews link raw content
        // Step 6: Extract all reviews in loop 

        // TODO: Fetch paginated products from DB
        const productNames = [
            "iphone 12"
        ]

        for (const productName in productNames) {
            let rawProductSearchContent = await searchProduct(productName);
            let detailPageLink = await extractDetailPageLink(rawProductSearchContent);
            console.log(chalk.bgWhite(detailPageLink));
            let detailPageContent = await fetchDetailPageContent(detailPageLink);
            const reviewsUrl = await detailsReviewPageURL(detailPageContent)
            console.log(chalk.bgGreen(reviewsUrl));
            await extractReviews(reviewsUrl)
        }
    } catch (error) {
        console.log(chalk.red("Error in main fnc: ", error));
    }
}

main()

