import { delay, BASE_URL, SEARCH_API_URL } from "../settings.js";
import { Storage } from '@google-cloud/storage';
import path from "path";

const storage = new Storage({ projectId: 'reviewdale-datafabric' });

export async function fetchJson(page, url) {
    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 60000 });
    const body = await page.evaluate(() => document.body.innerText);
    return body;
}

export async function fetchHtml(page, url) {
    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await delay(1000 + Math.floor(Math.random() * 2000));
    return await page.content();
}

export function safeFilename(str, prefix = '') {
    return prefix + str.replace(/[^a-zA-Z0-9]+/g, "_") + ".html";
}

export async function extractDetailUrlFromSearchHtml(html) {
    const match = html.match(/<div[^>]*class="sm-product[^"]*"[^>]*>[\s\S]*?<a[^>]*href="([^"]+)"[^>]*>/);
    if (match && match[1]) {
        return BASE_URL + match[1];
    }
    return null;
}

export async function uploadProductInfo(productInfo, productId, bucketName = "rd-bckt-df-data-harvester", folderPath = "C001/product/2025-08-01") {
    const fileName = `${productId}.json`;
    const filePath = path.posix.join(folderPath, fileName);

    await uploadJsonToGCP(
        productInfo,
        bucketName,
        filePath,
        `product information (${productId})`
    );
}

export function randInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

export async function randomMouseMoves(page, n = 3) {
    for (let i = 0; i < n; i++) {
        const x = randInt(50, 1000);
        const y = randInt(50, 700);
        await page.mouse.move(x, y, { steps: randInt(5, 25) });
        await new Promise(res => setTimeout(res, randInt(50, 300)));
    }
}

export async function randomScroll(page) {
    for (let i = 0; i < randInt(1, 3); i++) {
        const scrollBy = randInt(100, 800);
        await page.evaluate(y => window.scrollBy(0, y), scrollBy);
        await new Promise(res => setTimeout(res, randInt(100, 500)));
    }
}

export function matchProductName(productName, products) {
    const keywordsToExclude = new Set([]);

    const specKeywordsPattern = /\b(?:\d+gb|ram|\d+g)\b/gi;

    let cleanedName = productName.replace(specKeywordsPattern, '');
    cleanedName = cleanedName.replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim().toLowerCase();
    const searchWords = new Set(cleanedName.split(' '));

    const matchedProducts = [];

    for (const product of products || []) {
        let name = product.name || '';
        let nameCleaned = name.toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();

        if ([...keywordsToExclude].some(keyword => nameCleaned.includes(keyword))) {
            continue;
        }

        const allMatch = [...searchWords].every(word => nameCleaned.includes(word));
        if (allMatch) {
            matchedProducts.push(product);
        }
    }

    return matchedProducts;
}

/**
 * Generic function to fetch JSON data from GCP Storage
 * @param {string} bucketName - Name of the GCP Storage bucket
 * @param {string} filePath - Path to the file within the bucket
 * @param {string} description - Description for logging purposes
 * @returns {Promise<any>} - Parsed JSON data from the file
 */
export async function fetchJsonFromGCP(bucketName, filePath, description = 'data') {
    try {
        console.log(`🔄 Fetching ${description} from GCP Storage...`);
        console.log(`📍 Location: gs://${bucketName}/${filePath}`);

        // Fetch from GCP Storage
        const bucket = storage.bucket(bucketName);
        const file = bucket.file(filePath);

        // Check if file exists
        const [exists] = await file.exists();
        if (!exists) {
            throw new Error(`File not found at gs://${bucketName}/${filePath}`);
        }

        // Download and parse the file
        const [contents] = await file.download();
        const jsonData = JSON.parse(contents.toString());

        console.log(`✅ Successfully loaded ${description} from GCP Storage`);
        return jsonData;

    } catch (error) {
        console.log(`❌ Error fetching ${description} from GCP: ${error.message}`);
        throw new Error(`Failed to fetch ${description} from gs://${bucketName}/${filePath}: ${error.message}`);
    }
}

/**
 * Fetch the centralized product dataset from GCP Storage
 * @returns {Promise<Array>} - Array of products from centralized dataset
 */
export async function fetchCentralizedProductDataset() {
    try {
        const productData = await fetchJsonFromGCP(
            'rd-bckt-source-data',
            'rd-entity/rd-products.json',
            'centralized product dataset'
        );

        // Validate the data structure
        if (!Array.isArray(productData)) {
            throw new Error('Invalid product dataset format: expected an array');
        }

        console.log(`📊 Dataset contains ${productData.length} products`);
        return productData;

    } catch (error) {
        console.log(`❌ Centralized product dataset unavailable: ${error.message}`);
        throw new Error(`Centralized product dataset is required but unavailable: ${error.message}`);
    }
}

/**
 * Get centralized product dataset from GCP Storage (GCP-only, no fallback)
 * @returns {Promise<Array>} - Array of products from centralized dataset
 */
export async function getProductDataset() {
    console.log('🌐 Using GCP-only centralized product dataset...');
    return await fetchCentralizedProductDataset();
}

