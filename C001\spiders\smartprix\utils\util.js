import { delay, BASE_URL, SEARCH_API_URL } from "../settings.js";
import { Storage } from '@google-cloud/storage';
import path from "path";

const storage = new Storage({ projectId: 'reviewdale-datafabric' });

// Cache for centralized product dataset to avoid repeated downloads
let cachedProductDataset = null;
let cacheTimestamp = null;
const CACHE_DURATION_MS = 30 * 60 * 1000; // 30 minutes

export async function fetchJson(page, url) {
    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 60000 });
    const body = await page.evaluate(() => document.body.innerText);
    return body;
}

export async function fetchHtml(page, url) {
    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await delay(1000 + Math.floor(Math.random() * 2000));
    return await page.content();
}

export function safeFilename(str, prefix = '') {
    return prefix + str.replace(/[^a-zA-Z0-9]+/g, "_") + ".html";
}

export async function extractDetailUrlFromSearchHtml(html) {
    const match = html.match(/<div[^>]*class="sm-product[^"]*"[^>]*>[\s\S]*?<a[^>]*href="([^"]+)"[^>]*>/);
    if (match && match[1]) {
        return BASE_URL + match[1];
    }
    return null;
}

export async function uploadProductInfo(productInfo, productId, bucketName = "rd-bckt-df-data-harvester", folderPath = "C001/product/2025-08-01") {
    // TODO: Make folder name dynamic from build trigger

    const fileName = `${productId}.json`;
    const filePath = path.posix.join(folderPath, fileName);

    const bucket = storage.bucket(bucketName);
    const file = bucket.file(filePath);

    await file.save(JSON.stringify(productInfo, null, 2), {
        contentType: "application/json",
    });

    console.log(`Success: Product Information saved to gs://${bucketName}/${filePath}`);
}

export function randInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

export async function randomMouseMoves(page, n = 3) {
    for (let i = 0; i < n; i++) {
        const x = randInt(50, 1000);
        const y = randInt(50, 700);
        await page.mouse.move(x, y, { steps: randInt(5, 25) });
        await new Promise(res => setTimeout(res, randInt(50, 300)));
    }
}

export async function randomScroll(page) {
    for (let i = 0; i < randInt(1, 3); i++) {
        const scrollBy = randInt(100, 800);
        await page.evaluate(y => window.scrollBy(0, y), scrollBy);
        await new Promise(res => setTimeout(res, randInt(100, 500)));
    }
}

export function matchProductName(productName, products) {
    const keywordsToExclude = new Set([]);

    const specKeywordsPattern = /\b(?:\d+gb|ram|\d+g)\b/gi;

    let cleanedName = productName.replace(specKeywordsPattern, '');
    cleanedName = cleanedName.replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim().toLowerCase();
    const searchWords = new Set(cleanedName.split(' '));

    const matchedProducts = [];

    for (const product of products || []) {
        let name = product.name || '';
        let nameCleaned = name.toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();

        if ([...keywordsToExclude].some(keyword => nameCleaned.includes(keyword))) {
            continue;
        }

        const allMatch = [...searchWords].every(word => nameCleaned.includes(word));
        if (allMatch) {
            matchedProducts.push(product);
        }
    }

    return matchedProducts;
}

/**
 * Fetch the centralized product dataset from GCP Storage
 * @param {boolean} forceRefresh - Force refresh of cached data
 * @returns {Promise<Array>} - Array of products from centralized dataset
 */
export async function fetchCentralizedProductDataset(forceRefresh = false) {
    try {
        // Check if we have valid cached data
        const now = Date.now();
        if (!forceRefresh && cachedProductDataset && cacheTimestamp && (now - cacheTimestamp) < CACHE_DURATION_MS) {
            console.log('📋 Using cached centralized product dataset');
            return cachedProductDataset;
        }

        console.log('🔄 Fetching centralized product dataset from GCP...');

        // Fetch from GCP Storage
        const bucket = storage.bucket('rd-bckt-source-data');
        const file = bucket.file('rd-entity/rd-products.json');

        // Check if file exists
        const [exists] = await file.exists();
        if (!exists) {
            throw new Error('Centralized product dataset file not found at rd-entity/rd-products.json');
        }

        // Download and parse the file
        const [contents] = await file.download();
        const productData = JSON.parse(contents.toString());

        // Validate the data structure
        if (!Array.isArray(productData)) {
            throw new Error('Invalid product dataset format: expected an array');
        }

        // Cache the data
        cachedProductDataset = productData;
        cacheTimestamp = now;

        console.log(`✅ Successfully loaded ${productData.length} products from centralized dataset`);
        return productData;

    } catch (error) {
        console.log(`❌ Error fetching centralized product dataset: ${error.message}`);

        // If we have cached data, use it as fallback
        if (cachedProductDataset) {
            console.log('⚠️  Using cached data as fallback');
            return cachedProductDataset;
        }

        // If no cached data available, throw error
        throw new Error(`Failed to fetch centralized product dataset: ${error.message}`);
    }
}

/**
 * Get product dataset with fallback to local PRODUCTS array
 * @param {boolean} forceRefresh - Force refresh of cached data
 * @returns {Promise<Array>} - Array of products
 */
export async function getProductDataset(forceRefresh = false) {
    try {
        // Try to get centralized dataset first
        return await fetchCentralizedProductDataset(forceRefresh);
    } catch (error) {
        console.log(`⚠️  Centralized dataset unavailable: ${error.message}`);

        // Fallback to local PRODUCTS array if available
        try {
            const { PRODUCTS } = await import('../settings.js');
            console.log(`📋 Using local PRODUCTS array as fallback (${PRODUCTS.length} products)`);
            return PRODUCTS;
        } catch (fallbackError) {
            console.log(`❌ Local fallback also failed: ${fallbackError.message}`);
            throw new Error('Both centralized and local product datasets are unavailable');
        }
    }
}

