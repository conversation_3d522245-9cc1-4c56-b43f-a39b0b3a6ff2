import { delay, BASE_URL, SEARCH_API_URL } from "../settings.js";
import { Storage } from '@google-cloud/storage';
import path from "path";

const storage = new Storage({ projectId: 'reviewdale-datafabric' });

export async function fetchJson(page, url) {
    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 60000 });
    const body = await page.evaluate(() => document.body.innerText);
    return body;
}

export async function fetchHtml(page, url) {
    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 60000 });
    await delay(1000 + Math.floor(Math.random() * 2000));
    return await page.content();
}

export function safeFilename(str, prefix = '') {
    return prefix + str.replace(/[^a-zA-Z0-9]+/g, "_") + ".html";
}

export async function extractDetailUrlFromSearchHtml(html) {
    const match = html.match(/<div[^>]*class="sm-product[^"]*"[^>]*>[\s\S]*?<a[^>]*href="([^"]+)"[^>]*>/);
    if (match && match[1]) {
        return BASE_URL + match[1];
    }
    return null;
}

export async function uploadProductInfo(productInfo, productId, bucketName = "rd-bckt-df-data-harvester", folderPath = "C001/product/2025-08-01") {
    // TODO: Make folder name dynamic from build trigger

    const fileName = `${productId}.json`;
    const filePath = path.posix.join(folderPath, fileName);

    const bucket = storage.bucket(bucketName);
    const file = bucket.file(filePath);

    await file.save(JSON.stringify(productInfo, null, 2), {
        contentType: "application/json",
    });

    console.log(`Success: Product Information saved to gs://${bucketName}/${filePath}`);
}

export function randInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

export async function randomMouseMoves(page, n = 3) {
    for (let i = 0; i < n; i++) {
        const x = randInt(50, 1000);
        const y = randInt(50, 700);
        await page.mouse.move(x, y, { steps: randInt(5, 25) });
        await new Promise(res => setTimeout(res, randInt(50, 300)));
    }
}

export async function randomScroll(page) {
    for (let i = 0; i < randInt(1, 3); i++) {
        const scrollBy = randInt(100, 800);
        await page.evaluate(y => window.scrollBy(0, y), scrollBy);
        await new Promise(res => setTimeout(res, randInt(100, 500)));
    }
}

export function matchProductName(productName, products) {
    const keywordsToExclude = new Set([]);

    const specKeywordsPattern = /\b(?:\d+gb|ram|\d+g)\b/gi;

    let cleanedName = productName.replace(specKeywordsPattern, '');
    cleanedName = cleanedName.replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim().toLowerCase();
    const searchWords = new Set(cleanedName.split(' '));

    const matchedProducts = [];

    for (const product of products || []) {
        let name = product.name || '';
        let nameCleaned = name.toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();

        if ([...keywordsToExclude].some(keyword => nameCleaned.includes(keyword))) {
            continue;
        }

        const allMatch = [...searchWords].every(word => nameCleaned.includes(word));
        if (allMatch) {
            matchedProducts.push(product);
        }
    }

    return matchedProducts;
}

