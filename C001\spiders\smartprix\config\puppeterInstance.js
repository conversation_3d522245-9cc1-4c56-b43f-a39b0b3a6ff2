// puppeteerInstance.js
import puppeteer from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';

puppeteer.use(StealthPlugin());

let browser = null;

export async function getBrowser() {
    if (!browser) {
        browser = await puppeteer.launch({
            headless: false
        });
    }
    return browser;
}

// export async function getBrowser() {
//     if (!browser) {
//         browser = await puppeteer.launch({
//             headless: 'new',
//             dumpio: false,
//             args: [
//                 '--no-sandbox',
//                 '--disable-setuid-sandbox',
//                 '--disable-dev-shm-usage',
//                 '--disable-gpu',
//                 '--window-size=1200,800',
//                 '--single-process'
//             ],
//             executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || '/usr/bin/chromium',
//             protocolTimeout: 120000
//         });

//     }
//     return browser;
// }

export async function getPage() {
    const browser = await getBrowser();
    const page = await browser.newPage();
    await page.setViewport({ width: 1200, height: 800 });
    await page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36'
    );
    await page.setExtraHTTPHeaders({ 'accept-language': 'en-US,en;q=0.9' });
    return page;
}

export async function closeBrowser() {
    if (browser) {
        await browser.close();
        browser = null;
    }
}
