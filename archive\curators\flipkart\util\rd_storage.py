from google.cloud import storage
<<<<<<< Updated upstream
from dotenv import load_dotenv # type: ignore
import os
import json
load_dotenv()

project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
rd_bucket_name = os.getenv('RD_DF_RAW_DATA_BUCKET_NAME')
rd_storage_client = storage.Client(project=project_id)

def list_files_from_storage(folder_name):
    if not folder_name:
        raise ValueError("FOLDER_TO_PROCESS environment variable is not set.")
    if not folder_name.endswith('/'):
        folder_name += '/'
    blobs = rd_storage_client.list_blobs('rd-bckt-df-data-harvester', prefix=folder_name)
    return [blob.name for blob in blobs]

def read_file_content(file_name):
    rd_bucket = rd_storage_client.bucket('rd-bckt-df-data-harvester')
    blob = rd_bucket.blob(file_name)
    
    if not blob.exists():
        raise FileNotFoundError(f"Not found: '{file_name}' in bucket.")
    
    text_data = blob.download_as_text()
    return json.loads(text_data) 
=======
from dotenv import load_dotenv  # type: ignore
import os
import json

load_dotenv()

project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
bucket_name = os.getenv('RD_DF_RAW_DATA_BUCKET_NAME')
input_dir = os.getenv('INPUT_DIR')

storage_client = storage.Client(project=project_id)

def list_input_file_paths(input_dir):
    if not input_dir:
        raise ValueError("Error: INPUT_DIR not set.")
    if not input_dir.endswith('/'):
        input_dir += '/'
    
    blob_list = storage_client.list_blobs(bucket_name, prefix=input_dir)
    input_file_list = [blob.name for blob in blob_list]
    return input_file_list

def read_input_file_content(input_file_path):
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(input_file_path)
    
    if not blob.exists():
        raise FileNotFoundError(f"Not found: '{input_file_path}' in '{bucket_name}'.")
    
    input_file_data = blob.download_as_text()
    return json.loads(input_file_data)
>>>>>>> Stashed changes
