import pandas as pd

def curate_phone_data(data):
    try:
        df = pd.DataFrame(data)

        df['WIDTH_MM'] = df['WIDTH_MM'].str.replace(',', '.')
        df['HEIGHT_MM'] = df['HEIGHT_MM'].str.replace(',', '.')
        df['DEPTH_MM'] = df['DEPTH_MM'].str.replace(',', '.')
        df['MASS_G'] = df['MASS_G'].str.replace(',', '.')
        df['DISPLAY_DIAGONAL_MM'] = df['DISPLAY_DIAGONAL_MM'].str.replace(',', '.')

        df['MODEL_A'] = df['MODEL'].str.split(' ').str[0]
        df['SERIES'] = df['MODEL'].str.split(' ').str[1]
        df['VARIANT'] = df['MODEL'].str.replace(r' [A-Z]+[0-9]+-[0-9]+$', '').str.replace(' Dual SIM', '').str.replace(' LTE', '').str.replace(' TD-LTE', '')

        df['RELEASED_DATE'] = pd.to_datetime(df['RELEASED'], format='%Y %b %d', errors='coerce').fillna(pd.to_datetime(df['RELEASED'], format='%Y %b', errors='coerce'))
        df['DIMENSION_WIDTH'] = df['WIDTH_MM'].astype(float) * 0.1
        df['DIMENSION_DEPTH'] = df['DEPTH_MM'].astype(float) * 0.1
        df['DIMENSION_HEIGHT'] = df['HEIGHT_MM'].astype(float) * 0.1
        df['WEIGHT'] = df['MASS_G'].astype(float) * 0.001
        df['DISPLAY_SIZE'] = df['DISPLAY_DIAGONAL_MM'].astype(float) * 0.1

        df_curated = df[['BRAND', 'MODEL_A', 'SERIES', 'VARIANT', 'RELEASED_DATE', 'DIMENSION_WIDTH', 'DIMENSION_DEPTH', 'DIMENSION_HEIGHT', 'WEIGHT', 'DISPLAY_SIZE', 'OPERATING_SYSTEM', 'DEVICE_CATEGORY', 'CPU', 'RAM_CAPACITY_MIB_RAM', 'NON_VOLATILE_MEMORY_CAPACITY_MIB_NON_VOLATILE', 'RESOLUTION', 'DISPLAY_TYPE', 'EXPANSION_INTERFACES', 'USB_CONNECTOR', 'NUMBER_OF_EFFECTIVE_PIXELS_MP_CAMERA', 'SECONDARY_CAMERA_NUMBER_OF_PIXELS_MP_SEC_CAM', 'NOMINAL_BATTERY_CAPACITY_MAH_BATTERY', 'MARKET_COUNTRIES', 'IMAGE', 'ADDED']]
        df_curated.columns = ['BRAND', 'MODEL_A', 'SERIES', 'VARIANT', 'RELEASED_DATE', 'DIMENSION_WIDTH', 'DIMENSION_DEPTH', 'DIMENSION_HEIGHT', 'WEIGHT', 'DISPLAY_SIZE', 'OPERATING_SYSTEM', 'DEVICE_CATEGORY', 'CPU_NAME', 'MEMORY_RAM_CAPACITY', 'MEMORY_STORAGE_CAPACITY', 'RESOLUTION', 'DISPLAY_TYPE', 'MEMORY_IS_EXPANDABLE', 'CONNECTOR_TYPE', 'CAMERA_PRIMARY_RESOLUTION', 'CAMERA_SECONDARY_RESOLUTION', 'BATTERY_CAPACITY', 'AVAILABLE_COUNTRIES', 'PATH_IMAGE', 'ADDED_ON']

        return "ok"
    except Exception as e:
        print(e)
        return str(e)
