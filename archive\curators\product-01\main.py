from google.cloud import storage
import json
from utils.util import identify_attributes
from data_pump.data_pump import ingest_product_data
import os
from collections import OrderedDict

project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
rd_bucket_name = os.getenv('RD_DF_RAW_DATA_BUCKET_NAME')
phone_price_dict = OrderedDict()

def read_files_from_rd_bucket(folder_name):
    rd_storage_client = storage.Client(project=project_id)
    raw_data_files = rd_storage_client.list_blobs('rd-bckt-df-data-harvester', prefix=folder_name)
    return raw_data_files

def read_file_content(file_name):
    rd_storage_client = storage.Client(project=project_id)
    rd_bucket = rd_storage_client.bucket('rd-bckt-df-data-harvester')
    raw_content = rd_bucket.blob(file_name)
    raw_data = raw_content.download_as_text()
    return raw_data

def start_curator():
    batches = 28
    print("In start curator.")
    for batch in range(1, batches+1):
        try:    
            file_name = f'C001/product/02/C001-raw-data-{batch}.json'
            raw_content = read_file_content(file_name)
            converted_data_object = json.loads(raw_content)
            curated_data = identify_attributes(converted_data_object, phone_price_dict)
            # print(curated_data)
            ingest_product_data(curated_data)
            print(f"Curated data ingested for batch: C001/C001-raw-data-{batch}.json")
            print('---')
    
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON from raw_file C001/C001-raw-data-{batch}.json: {e}")
        except Exception as e:
            print(f"Error processing raw_file C001/C001-raw-data-{batch}.json: {e}")

def clean_string(s):
    return s.replace("\n", "").strip()

def load_additional_attribute_file():

    for raw_file in read_files_from_rd_bucket('C001/product/02'):
        try:
            
            content = read_file_content(raw_file.name)
            converted_data_object = json.loads(content)
            for item_list in converted_data_object:
                if item_list is not None:  
                    for item in item_list:
                        if item is not None:                              
                            data_price = item.get('dataPrice', 'Unknown')
                            phone_name = clean_string(item.get('phoneName', 'Unknown'))
                            product_id = item.get('productId', 'Unknown')
                            
                            phone_price_dict[phone_name] = {
                                'dataPrice': data_price,
                                'productId': product_id
                            }
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON from raw_file {raw_file.name}: {e}")
        except Exception as e:
            print(f"Error processing raw_file {raw_file.name}: {e}")
    start_curator()


load_additional_attribute_file()