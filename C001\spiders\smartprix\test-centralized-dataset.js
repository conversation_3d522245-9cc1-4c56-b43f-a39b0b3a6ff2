/**
 * Test script for Centralized Product Dataset functionality
 * This script tests the GCP dataset fetching and comparison logic
 */

import { getProductDataset, fetchCentralizedProductDataset, matchProductName } from './utils/util.js';

/**
 * Test the centralized dataset fetching
 */
async function testCentralizedDatasetFetching() {
    console.log('🧪 Testing Centralized Dataset Fetching...\n');
    
    try {
        console.log('📡 Attempting to fetch centralized dataset from GCP...');
        const startTime = Date.now();
        
        const centralizedProducts = await fetchCentralizedProductDataset();
        
        const endTime = Date.now();
        const fetchTime = endTime - startTime;
        
        console.log(`✅ Successfully fetched centralized dataset`);
        console.log(`📊 Dataset size: ${centralizedProducts.length} products`);
        console.log(`⏱️  Fetch time: ${fetchTime}ms`);
        
        // Validate data structure
        if (centralizedProducts.length > 0) {
            const sampleProduct = centralizedProducts[0];
            console.log(`📋 Sample product structure:`, JSON.stringify(sampleProduct, null, 2));
            
            // Check for required fields
            const hasName = sampleProduct.product_name || sampleProduct.name || sampleProduct.title;
            const hasId = sampleProduct.id || sampleProduct.product_id;
            
            console.log(`🔍 Data validation:`);
            console.log(`   Has name field: ${hasName ? '✅' : '❌'}`);
            console.log(`   Has ID field: ${hasId ? '✅' : '❌'}`);
        }
        
        return centralizedProducts;
        
    } catch (error) {
        console.log(`❌ Failed to fetch centralized dataset: ${error.message}`);
        return null;
    }
}

/**
 * Test the fallback mechanism
 */
async function testFallbackMechanism() {
    console.log('\n🧪 Testing Fallback Mechanism...\n');
    
    try {
        console.log('📡 Testing getProductDataset with fallback...');
        const products = await getProductDataset();
        
        console.log(`✅ Successfully got product dataset`);
        console.log(`📊 Dataset size: ${products.length} products`);
        
        return products;
        
    } catch (error) {
        console.log(`❌ Both centralized and fallback failed: ${error.message}`);
        return null;
    }
}

/**
 * Test the product comparison logic with centralized data
 */
async function testProductComparison(centralizedProducts) {
    console.log('\n🧪 Testing Product Comparison Logic...\n');
    
    if (!centralizedProducts || centralizedProducts.length === 0) {
        console.log('❌ No centralized products available for testing');
        return;
    }
    
    // Test cases: mix of existing and potentially new products
    const testProducts = [
        // These might be found as EXISTING (depending on centralized dataset)
        { name: 'Samsung Galaxy S24 Ultra', expected: 'EXISTING' },
        { name: 'iPhone 15 Pro Max', expected: 'EXISTING' },
        { name: 'OnePlus 12', expected: 'EXISTING' },
        
        // These should likely be found as NEW (uncommon products)
        { name: 'Xiaomi 14 Ultra Global Edition', expected: 'NEW' },
        { name: 'Nothing Phone 3 Pro', expected: 'NEW' },
        { name: 'Realme GT 6 Pro Max', expected: 'NEW' },
        
        // Edge cases with variations
        { name: 'Samsung Galaxy S24 Ultra 5G', expected: 'EXISTING' },
        { name: 'iPhone 15 Pro Max 256GB', expected: 'EXISTING' },
    ];
    
    console.log(`📊 Testing against ${centralizedProducts.length} centralized products...\n`);
    
    let correctPredictions = 0;
    let totalTests = testProducts.length;
    
    for (const testProduct of testProducts) {
        const isExisting = isProductAlreadyExists(testProduct.name, centralizedProducts);
        const actualResult = isExisting ? 'EXISTING' : 'NEW';
        
        // For this test, we'll consider any result as potentially correct since we don't know the exact centralized dataset
        const statusIcon = '🔍';
        const resultIcon = actualResult === 'EXISTING' ? '⏭️' : '🆕';
        
        console.log(`${statusIcon} ${resultIcon} ${testProduct.name}`);
        console.log(`   Result: ${actualResult}`);
        
        if (isExisting) {
            // Show which product it matched with
            const matches = getProductMatches(testProduct.name, centralizedProducts);
            if (matches.length > 0) {
                console.log(`   Matched with: ${matches[0].name}`);
            }
        }
        console.log('');
    }
    
    console.log('='.repeat(60));
    console.log(`📈 Comparison test completed for ${totalTests} products`);
    console.log('='.repeat(60));
}

/**
 * Helper function to check if product exists (same as in newProducts.js)
 */
function isProductAlreadyExists(scrapedProductName, existingProducts) {
    const existingProductsFormatted = existingProducts.map(product => ({
        name: product.product_name || product.name || product.title || '',
        id: product.id || product.product_id || ''
    }));
    
    const matches = matchProductName(scrapedProductName, existingProductsFormatted);
    return matches.length > 0;
}

/**
 * Helper function to get product matches
 */
function getProductMatches(scrapedProductName, existingProducts) {
    const existingProductsFormatted = existingProducts.map(product => ({
        name: product.product_name || product.name || product.title || '',
        id: product.id || product.product_id || ''
    }));
    
    return matchProductName(scrapedProductName, existingProductsFormatted);
}

/**
 * Test caching mechanism
 */
async function testCaching() {
    console.log('\n🧪 Testing Caching Mechanism...\n');
    
    try {
        console.log('📡 First fetch (should download from GCP)...');
        const startTime1 = Date.now();
        const products1 = await fetchCentralizedProductDataset();
        const endTime1 = Date.now();
        
        console.log(`⏱️  First fetch time: ${endTime1 - startTime1}ms`);
        
        console.log('📋 Second fetch (should use cache)...');
        const startTime2 = Date.now();
        const products2 = await fetchCentralizedProductDataset();
        const endTime2 = Date.now();
        
        console.log(`⏱️  Second fetch time: ${endTime2 - startTime2}ms`);
        
        const isCacheWorking = (endTime2 - startTime2) < (endTime1 - startTime1) / 2;
        console.log(`🚀 Cache working: ${isCacheWorking ? '✅ Yes' : '❌ No'}`);
        
        console.log('🔄 Testing force refresh...');
        const startTime3 = Date.now();
        const products3 = await fetchCentralizedProductDataset(true);
        const endTime3 = Date.now();
        
        console.log(`⏱️  Force refresh time: ${endTime3 - startTime3}ms`);
        
    } catch (error) {
        console.log(`❌ Caching test failed: ${error.message}`);
    }
}

/**
 * Run all tests
 */
async function runAllTests() {
    console.log('🚀 Starting Centralized Dataset Tests...\n');
    
    try {
        // Test 1: Centralized dataset fetching
        const centralizedProducts = await testCentralizedDatasetFetching();
        
        // Test 2: Fallback mechanism
        await testFallbackMechanism();
        
        // Test 3: Product comparison (if we have centralized data)
        if (centralizedProducts) {
            await testProductComparison(centralizedProducts);
        }
        
        // Test 4: Caching mechanism
        await testCaching();
        
        console.log('\n✅ All centralized dataset tests completed!');
        
    } catch (error) {
        console.log('\n❌ Test failed with error:', error.message);
        process.exit(1);
    }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runAllTests();
}

export { testCentralizedDatasetFetching, testFallbackMechanism, testProductComparison, testCaching };
