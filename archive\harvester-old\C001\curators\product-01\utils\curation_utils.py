import re
from datetime import datetime
import json
import random
import logging
from data_pump.data_pump import is_brand_id_present, is_product_id_present
from decimal import Decimal, InvalidOperation

logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')

def extract_inches(s):
    if s is None:
        logging.info("extract_inches received None as input")
        return None
    try:
        match = re.search(r'(\d+(\.\d+)?)\s*inches', s)
        if match:
            return match.group(1)
    except Exception as e:
        logging.info(f"extract_inches failed: {s} - Error: {e}")
    return None


def convert_date(date_str):
    try:
        if date_str is None:
            logging.info("convert_date received None as date_str")
            return ""

        cleaned_date_str = date_str.replace(' (Official)', '').replace(' (Expected)', '').replace(' (Last Known)', '')

        months = {
            "January": "01",
            "February": "02",
            "March": "03",
            "April": "04",
            "May": "05",
            "June": "06",
            "July": "07",
            "August": "08",
            "September": "09",
            "October": "10",
            "November": "11",
            "December": "12"
        }

        month, day, year = cleaned_date_str.replace(',', '').split(' ')
        month_number = months[month]
        day_number = day.zfill(2)

        return f"{year}-{month_number}-{day_number}"
    except Exception as e:
        logging.info(f"Failed to convert date: {date_str} - Error: {e}")
        return ""

def get_colors(color_str):
    try:
        return [color.strip() for color in color_str.split(',')]
    except Exception as e:
        logging.info(f"get_colors failed: {color_str} - Error: {e}")
        return []

def get_dimensions(data):
    if data is None:
        logging.info("get_dimensions received None as input")
        return ["", "", ""]

    height = ""
    width = ""
    thickness = ""

    def extract_numeric(description):
        try:
            return re.sub(r'[^\d.]', '', description).strip()
        except Exception as e:
            logging.error(f"extract_numeric failed - Error: {e}")
            return None

    try:
        for item in data:
            if item['title'] == "Height":
                height = extract_numeric(item['description'])
            elif item['title'] == "Width":
                width = extract_numeric(item['description'])
            elif item['title'] == "Thickness":
                thickness = extract_numeric(item['description'])
    except Exception as e:
        logging.error(f"get_dimensions failed - Error: {e}")

    return [height, width, thickness]


def get_weight(data):
    if data is None:
        logging.info("get_weight received None as input")
        return ""

    weight = ""

    def extract_numeric(description):
        try:
            return re.sub(r'[^\d.]', '', description).strip()
        except Exception as e:
            logging.error(f"extract_numeric failed - Error: {e}")
            return None
    try:
        for item in data:
            if item['title'] == "Weight":
                weight = extract_numeric(item['description'])
    except Exception as e:
        logging.error(f"get_weight failed - Error: {e}")

    return weight


def get_camera_descriptions(data):
    rear_camera = ""
    front_camera = ""

    try:
        for item in data:
            if item['title'] == "Rear Camera":
                rear_camera = item['description'].strip()
            elif item['title'] == "Front Camera":
                front_camera = item['description'].strip()

        if len(rear_camera) <= 1 or len(front_camera) <= 1:
            return None
    except Exception as e:
        logging.error(f"get_camera_descriptions failed - Error: {e}")
        return None

    return { "primary": rear_camera, "secondary": front_camera }

def get_internal_memory(data):
    if data is None:
        logging.info("get_internal_memory received None as input")
        return None
    try:
        match = re.search(r'(\d+\s*GB)', data, re.IGNORECASE)
        if match:
            return match.group(1).strip()
    except Exception as e:
        logging.error(f"get_internal_memory failed: {data} - Error: {e}")
    return None


def extract_bt_version(text):
    if text is None:
        logging.info("extract_bt_version received None as input")
        return ''
    try:
        version_match = re.search(r'v\d+(\.\d+)?', text)
        return version_match.group(0) if version_match else ''
    except Exception as e:
        logging.error(f"extract_bt_version failed: {text} - Error: {e}")
        return ''


def get_resolution_refresh_rate(data):
    descriptions = []
    if len(data) == 0:
        return ""
    last_item = data.pop(0)

    try:
        for item in data:
            if item['title'] == "Refresh Rate":
                descriptions.append(item['description'].strip())
        if last_item['title'] == "Refresh Rate":
            descriptions.append(last_item['description'].strip())
        elif last_item['title'] == "Resolution":
            descriptions.insert(0, last_item['description'].strip())
    except Exception as e:
        logging.error(f"get_resolution_refresh_rate failed - Error: {e}")

    return ', '.join(descriptions)

def get_usb(data):
    output = ''
    try:
        for item in data:
            if item['title'] == 'USB Type-C':
                output += item['title'] + ", "

        for item in data:
            if item['title'] != 'USB Type-C':
                output += item['description']
    except Exception as e:
        logging.error(f"get_usb failed - Error: {e}")
    return output

def extract_supported_network_types(strings):
    if strings is None:
        return None
    pattern = r'(\dG)(?! Not Supported)'

    supported_generations = []

    try:
        for string in strings:
            matches = re.findall(pattern, string)
            supported_generations.append(matches)
    except Exception as e:
        logging.error(f"extract_supported_network_types failed - Error: {e}")
        return []

    return supported_generations


def extract_wifi(wifi):
    try:
        if wifi is None:
            return []

        extracted_parts = []
        for string in wifi:
            if string == "No":
                extracted_parts.append(string)
            else:
                part = string.split("Yes, ", 1)[1]
                extracted_parts.append(part.strip())
        return extracted_parts
    except Exception as e:
        logging.error(f"extract_wifi failed - Error: {e}")
        return []

def get_ram(description):
    if description is None:
        logging.info("get_ram received None as input")
        return ''
    try:
        match = re.search(r'\b(\d+\s*(GB|KB|MB))\b', description)
        return match.group(1) if match else None
    except Exception as e:
        logging.error(f"get_ram failed - Error: {e}")
        return ''


def generate_product_id(category_id, brand_id):
    try:
        category_id = category_id[1:]
        brand_id = brand_id[1:]
        prefix = f"P{category_id}{brand_id}"
        random_num = f"{random.randint(1, 9999):04d}"
        random_product_id = f"{prefix}{random_num}"

        product_id = is_product_id_present(random_product_id)
        if product_id == None:
            return random_product_id
        else:
            return generate_product_id(category_id, brand_id)
    except Exception as e:
        logging.error(f"generate_product_id failed - Error: {e}")
        return ''

def generate_brand_id():
    random_number = random.randint(1, 9999)
    generated_brand_id = f"B{random_number:04d}"
    brand_id = is_brand_id_present(generated_brand_id)
    if brand_id == None:
        return generated_brand_id
    else:
        return generate_brand_id()

def curate_product_price(raw_price_value):
    if raw_price_value == "N/A":
        return None
    try:
        return Decimal(raw_price_value.replace(',', ''))
    except InvalidOperation:
        return None
    
def curate_battery_string(raw_battery):
    try:
        if raw_battery is None:
            logging.info("curate_battery_string received None as input")
            return None

        pattern = r'(\d+)\s*mAh'
        match = re.match(pattern, raw_battery)
        if match:
            return match.group(1)
        else:
            return None
    except Exception as e:
        logging.error(f"curate_battery_string failed - Error: {e}")
        return None

    
def curate_memory(raw_memory):
    if raw_memory is None:
        return None
    try:
        if " GB" in raw_memory:
            return raw_memory.replace(" GB", "")
        elif "GB" in raw_memory:
            return raw_memory.replace("GB", "")
        elif " MB" in raw_memory:
            return raw_memory.replace(" MB", "")
        elif " KB" in raw_memory:
            return raw_memory.replace(" KB", "")
        else:
            logging.info(f"Unsupported memory unit: {raw_memory}")
            return None
    except Exception as e:
        logging.info(f"Failed to curate memory: Error: {e} - {raw_memory}")
        return None

def extract_memory_unit(memory_string):
    try:
        if memory_string is None:
            logging.error("extract_memory_unit received None as input")
            return None
        pattern = re.compile(r'\b\d+\s*(GB|KB|MB)\b')
        match = pattern.search(memory_string)
        if match:
            return match.group(1)
        return None
    except Exception as e:
        logging.error(f"extract_memory_unit failed - Error: {e}")
        return None


def convert_memory_to_gb(value, unit):
    if value is None:
        return value
    if unit == 'GB':
        return value
    unit = unit.upper()
    if unit == 'KB':
        return int(value) / (1024 ** 2)  
    elif unit == 'MB':
        return int(value) / 1024  
    else:
        raise ValueError("Unit must be either 'KB' or 'MB'")