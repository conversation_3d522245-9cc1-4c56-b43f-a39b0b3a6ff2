steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-f', 'Dockerfile',
      '-t', 'asia-south1-docker.pkg.dev/reviewdale-datafabric/rd-artfct-data-harvester/curator:$_NAME',
      '--build-arg', '_FOLDER_PATH=$_FOLDER_PATH',
      '--build-arg', '_RUN_CMD=$_RUN_CMD',
      '.'
    ]
    dir: 'C001/curators'

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'asia-south1-docker.pkg.dev/reviewdale-datafabric/rd-artfct-data-harvester/curator:$_NAME']

  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
    entrypoint: "bash"
    args:
      - "-c"
      - |
        gcloud dataflow flex-template run "flipkart-cuator" \
          --template-file-gcs-location "gs://rd-bckt-source-data/flex_template.json" \
          --region asia-south1 \
          --temp-location gs://rd-bckt-df-pipeline/temp

options:
  default_logs_bucket_behavior: REGIONAL_USER_OWNED_BUCKET
