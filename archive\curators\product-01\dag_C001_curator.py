import sys
import os

sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from datetime import datetime, timedelta
from a_main import load_additional_attribute_file

today_date = datetime.today()
formatted_date = f"{today_date.year}, {today_date.month}, {today_date.day}"

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
    'start_date': today_date,
}

dag = DAG(
    'rd-DH-C001-Curator',
    default_args=default_args,
    description='DAG for C001 raw data curation',
    schedule_interval=timedelta(days=1),
)

run_my_function = PythonOperator(
    task_id='curator',
    python_callable=load_additional_attribute_file,
    dag=dag,
)
