GET_ALL_REVIEW_IDS_QUERY = "SELECT id FROM df_ppl_review"
INGEST_REVIEWS_QUERY =  """
                INSERT INTO df_ppl_review (
                    id, product_id, user_text, review_value, create_date, is_active, category_id, type_id, upvotes, downvotes, product_url, external_id, spider_id
                ) VALUES (
                    %s, %s, %s, %s, %s, 1, 'C001', 1, %s, %s, %s, %s, 2
                )
            """
INGEST_PRODUCT_QUERY = """
        INSERT INTO df_ppl_product (
            product_id, new_product_id, external_id, is_active, category_id, brand_id, brand, product_value, model, series, variant,
            attribute_1, attribute_2, attribute_3, attribute_4, attribute_5, attribute_7, attribute_8, attribute_9,
            attribute_10, attribute_11, attribute_12, attribute_13, attribute_14,
            attribute_15, attribute_16, attribute_17, attribute_18, attribute_19,
            attribute_20, attribute_21, attribute_22, attribute_23, attribute_24, create_date, spider_id
        ) VALUES (
            %s, %s, %s, 1, 'C001', %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, '02'
        )
    """
INGEST_PRODUCT_PRICES_QUERY = """
            INSERT INTO df_ppl_product_price (
                product_id, price, category_id, currency, country
            ) VALUES (
                %s, %s, 'C001', 'INR', 'IND'
            )
        """
INGEST_PRODUCT_IMAGES_QUERY = """
            INSERT INTO df_ppl_product_image (
                product_id, description, image_url, create_date, identifier
            ) VALUES (
                %s, %s, %s, %s, %s
            )
        """
INGEST_BRAND_QUERY = """
                    INSERT INTO mstr_brand (
                        id, name, is_active, create_date
                    ) VALUES (
                        %s, %s, 1, %s
                    )
                """
GET_BRAND_BY_ID = "SELECT id FROM mstr_brand WHERE id = %s LIMIT 1"
GET_BRAND_BY_NAME = "SELECT id FROM mstr_brand WHERE name = %s LIMIT 1"
GET_PRODUCT_BY_ID = "SELECT id FROM mstr_product WHERE id = %s"