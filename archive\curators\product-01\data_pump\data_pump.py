from data_pump.rd_db_config import connection_pool, get_connection_from_pool
from datetime import datetime, timezone
import mysql # type: ignore
import re
from decimal import Decimal
import logging
import json

create_date_gmt = datetime.now(timezone.utc)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def ingest_product_data(dataset):          
    print(dataset)  
    with get_connection_from_pool(connection_pool) as connection:
        if connection is None:
            logger.error("Failed to get database connection.")
            return

        with connection.cursor() as cursor:
            try:
                insert_product_data(cursor, dataset)
                insert_product_prices(cursor, dataset)
                insert_product_images(cursor, dataset)
                connection.commit()
                logger.info("Records inserted successfully")
            except mysql.connector.Error as err:
                logger.error(f"Error during data ingestion: {err}")
                connection.rollback()


def insert_product_data(cursor, dataset):
    rd_db_product_ingest = """
        INSERT INTO df_ppl_product (
            product_id, new_product_id, external_id, is_active, category_id, brand_id, brand, product_value, model, series, variant, 
            attribute_1, attribute_2, attribute_3, attribute_4, attribute_5, attribute_7, attribute_8, attribute_9, 
            attribute_10, attribute_11, attribute_12, attribute_13, attribute_14, 
            attribute_15, attribute_16, attribute_17, attribute_18, attribute_19, 
            attribute_20, attribute_21, attribute_22, attribute_23, attribute_24, create_date, ingester_id
        ) VALUES (
            %s, %s, %s, 1, 'C001', %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, '02'
        )
        """

    create_date_gmt = datetime.now(timezone.utc)

    product_values = [
        (
            item['product_id'], item['product_id'], item['external_id'], item['brand_id'], item['brand'], item['product_value'], 
            item['model'], item['series'], item['variant'],
            json.dumps(item.get('attribute_1', {})), json.dumps(item.get('attribute_2', {})), json.dumps(item.get('attribute_3', {})), 
            json.dumps(item.get('attribute_4', {})), 
            json.dumps(item.get('attribute_5', {})), json.dumps(item.get('attribute_7', {})), json.dumps(item.get('attribute_8', {})), 
            json.dumps(item.get('attribute_9', {})), json.dumps(item.get('attribute_10', {})), json.dumps(item.get('attribute_11', {})), 
            json.dumps(item.get('attribute_12', {})), json.dumps(item.get('attribute_13', {})), json.dumps(item.get('attribute_14', {})), 
            json.dumps(item.get('attribute_15', {})), json.dumps(item.get('attribute_16', {})), json.dumps(item.get('attribute_17', {})), 
            json.dumps(item.get('attribute_18', {})), json.dumps(item.get('attribute_19', {})), json.dumps(item.get('attribute_20', {})), 
            json.dumps(item.get('attribute_21', {})), json.dumps(item.get('attribute_22', {})), json.dumps(item.get('attribute_23', {})), 
            json.dumps(item.get('attribute_24', {})), create_date_gmt # <- Ensure this last value is included
        )
        for item in dataset
    ]


    cursor.executemany(rd_db_product_ingest, product_values)

def insert_product_prices(cursor, dataset):
    try: 
        if dataset is None:
            return
        rd_db_product_price_ingest = """
            INSERT INTO df_ppl_product_price (
                product_id, price, category_id, currency, country
            ) VALUES (
                %s, %s, 'C001', 'INR', 'IND'
            )
        """

        product_prices = [
            (
                item['product_id'], item['price']
            )
            for item in dataset
        ]

        cursor.executemany(rd_db_product_price_ingest, product_prices)
    except AttributeError as e:
        logging.error(f"insert_product_prices due to AttributeError: - Error: {e}")

def insert_product_images(cursor, dataset):
    try:
        if dataset is None:
            logging.error("insert_product_images received None as dataset")
            return
        if len(dataset) == 0:
            logging.error("insert_product_images received None as dataset")
            return
        rd_db_product_image_ingest = """
            INSERT INTO df_ppl_product_image (
                product_id, description, image_url, create_date, identifier
            ) VALUES (
                %s, %s, %s, %s, %s
            )
        """

        product_values = []
        for sublist in dataset:
            if not isinstance(sublist, dict):
                logging.warning(f"Skipping non-dictionary sublist: {sublist}")
                continue

            try:
                product_id = sublist.get("product_id", "")
                create_date = datetime.today().strftime('%Y-%m-%d')
                for image_url in sublist.get("product_images", []):
                    description = None
                    if image_url is None or len(image_url) == 0:
                        continue
                    image_type_pattern = r'(\d+)\.png$'
                    match = re.search(image_type_pattern, image_url)
                    if match:
                        if match.group(1) == '1':
                            product_values.append((product_id, 1, image_url, create_date_gmt, 1))
                        elif match.group(1) == '2':
                            product_values.append((product_id, 2, image_url, create_date_gmt, 2))
                        else:
                            product_values.append((product_id, description, image_url, create_date_gmt, description))
                    else:
                        product_values.append((product_id, description, image_url, create_date_gmt, description))
            except AttributeError as e:
                logging.warning(f"Skipping sublist due to AttributeError: {sublist} - Error: {e}")

        if product_values:
            cursor.executemany(rd_db_product_image_ingest, product_values)
        else:
            logging.info("No valid product images found to insert.")
    except AttributeError as e:
        logging.error(f"insert_product_images sublist due to AttributeError: {sublist} - Error: {e}")


def is_brand_id_present(brand_id):
    try:
        with get_connection_from_pool(connection_pool) as connection:
            if connection is None:
                logger.error("Failed to get database connection.")
                return None

            with connection.cursor() as cursor:
                check_brand_query = """
                    SELECT id
                    FROM mstr_brand
                    WHERE id = %s
                    LIMIT 1
                """
                cursor.execute(check_brand_query, (brand_id,))
                result = cursor.fetchone()
                return result
    except AttributeError as e:
        logging.error(f"is_brand_id_present sublist due to AttributeError: - Error: {e}")

def get_brand_id(brand):
    with get_connection_from_pool(connection_pool) as connection:
        if connection is None:
            logger.error("Failed to get database connection.")
            return None

        with connection.cursor() as cursor:
            check_brand_query = """
                SELECT id
                FROM mstr_brand
                WHERE name = %s
                LIMIT 1
            """
            cursor.execute(check_brand_query, (brand,))
            result = cursor.fetchone()
            return result

def is_product_id_present(product_id):
    with get_connection_from_pool(connection_pool) as connection:
        if connection is None:
            logger.error("Failed to get database connection.")
            return None

        with connection.cursor() as cursor:
            check_product_id_query = """
                SELECT id
                FROM mstr_product
                WHERE id = %s    
            """
            cursor.execute(check_product_id_query, (product_id,))
            result = cursor.fetchone()
            return result

def ingest_brand(brand_id, brand):
    with get_connection_from_pool(connection_pool) as connection:
        if connection is None:
            logger.error("Failed to get database connection.")
            return

        with connection.cursor() as cursor:
            try:
                insert_brand_query = """
                    INSERT INTO mstr_brand (
                        id, name, is_active, create_date
                    ) VALUES (
                        %s, %s, 1, %s
                    )
                """
                create_date = datetime.today().strftime('%Y-%m-%d')
                cursor.execute(insert_brand_query, (brand_id, brand, create_date_gmt))
                connection.commit()
                logger.info(f"New Brand Ingested: {brand_id}:{brand}")
            except mysql.connector.Error as err:
                logger.error(f"Error during brand ingestion: {err}")
                connection.rollback()
