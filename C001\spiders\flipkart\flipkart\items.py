import scrapy # type: ignore

class FlipkartReviewItem(scrapy.Item):
    product_id = scrapy.Field()
    review_id = scrapy.Field()
    product_name = scrapy.Field()
    review_value = scrapy.Field()
    review_title = scrapy.Field()
    review_text = scrapy.Field()
    upvotes = scrapy.Field()
    downvotes = scrapy.Field()
    username = scrapy.Field()
    date = scrapy.Field()
    product_url = scrapy.Field()
