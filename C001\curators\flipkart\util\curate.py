import random
import logging
from datetime import datetime, timedelta

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def rd_generate_review_ids(input_review_list, review_id_set):
    def generate_id():
        return 'R' + ''.join(random.choices('0123456789', k=9))

    for row in input_review_list:
        while True:
            new_id = generate_id()
            if new_id not in review_id_set:
                review_id_set.add(new_id)
                row['id'] = new_id
                break
    return input_review_list

def generate_random_timestamp(date_str):
    base_date = datetime.strptime(date_str, "%Y-%m-%d")
    random_time = timedelta(
        hours=random.randint(0, 23),
        minutes=random.randint(0, 59),
        seconds=random.randint(0, 59)
    )
    final_datetime = base_date + random_time
    return final_datetime.strftime("%Y-%m-%d %H:%M:%S")

def generate_rating_metrics(target_review_value):
    metrics = [
        round(random.uniform(max(1.0, target_review_value - 1.0), min(5.0, target_review_value + 1.0)), 1)
        for _ in range(5)
    ]
    sum_of_metrics = sum(metrics)
    sixth_metric = round(6.0 * target_review_value - sum_of_metrics, 1)

    if sixth_metric < 1.0 or sixth_metric > 5.0:
        adjustment = round((sixth_metric - 3.0) / 5.0, 1)
        metrics = [round(min(max(1.0, val + adjustment), 5.0), 1) for val in metrics]
        sixth_metric = round(6.0 * target_review_value - sum(metrics), 1)

    metrics.append(sixth_metric)
    return metrics

def curate_review_list(raw_review_list, review_id_set):
    input_review_list = generate_unique_review_ids(raw_review_list, review_id_set)

    for row in input_review_list:
        if row.get('product_url') is None:
            logger.info("Not found: Product link")
            return []
        row['date'] = generate_random_timestamp(row['date'])

    return input_review_list
