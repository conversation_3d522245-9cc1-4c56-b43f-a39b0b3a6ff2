# RD Data Harvester

This project is a Scrapy-based data scraper designed to extract product data from websites like 91mobiles and Cardekho. It's deployed on Google Cloud Platform (GCP) using Cloud Run, Cloud Build, and other services for scheduled and scalable data harvesting.

## Logical Overview

* **Targeted Scraping:** Extracts product data.
* **Modular Design:** Uses Scrapy (spiders, items, pipelines, middleware).
* **Data Extraction:** Uses XPath and lxml.
* **Scheduled Execution:** Automated using Cloud Scheduler.
* **Cloud-Based Deployment:** Containerized and deployed on Cloud Run.
* **Configurable Runs:** Controlled via Cloud Build substitutions.
* **Web Server Interface:** Uses Flask for Cloud Run.
* **Incremental Scraping:** Defaults to only scraping new items.

## Functional Overview

* **Spiders (`spiders/`)**: `91mobiles_spider.py`, `cardekho_spider.py`.
* **Items (`items.py`)**: `ProductItem` defines data structure.
* **Pipelines (`pipelines.py`)**:
    * `GCSJsonWriterPipeline`: Writes to `rd-bckt-df-data-harvester` GCS bucket.
    * `BigQueryPipeline`: Streams to `reviewdale-datafabric.rd-data-harvester.product` BigQuery table.
    * `CloudSqlPipeline`: Writes to Cloud SQL MySQL (stg/prod based on `_ENV`).
* **Middleware (`middlewares.py`)**: `RandomUserAgentMiddleware`.
* **Settings (`settings.py`)**: Configures Scrapy, reads Cloud Build variables.
* **Requirements (`requirements.txt`)**: Lists dependencies.
* **Dockerfile (`Dockerfile`)**: Defines Docker image.
* **Cloud Build (`cloudbuild.yaml`)**: Builds, pushes, deploys, sets env vars.
* **Web Server (`main.py`)**: Flask app for Cloud Run.

**Workflow:**

1.  Cloud Run receives web request.
2.  Flask (`main.py`) runs the Scrapy spider.
3.  Spiders extract data.
4.  Data is populated into `ProductItem` objects.
5.  Pipelines write data to GCS, BigQuery, or Cloud SQL.
6.  Cloud Build deploys the application.
7.  Cloud Scheduler triggers Cloud Run.

## Infrastructure Overview (GCP)

* **Cloud Run:** Serverless platform.
* **Cloud Scheduler:** Schedules Cloud Run.
* **Artifact Registry:** Stores Docker images.
* **Cloud Storage (GCS):** Stores data.
* **BigQuery:** Data warehousing.
* **Cloud SQL:** Relational database.
* **Cloud Logging:** Logs.
* **Cloud Build:** CI/CD.
* **Secret Manager:** Stores database credentials.

**Deployment Process:**

1.  Dockerization.
2.  Image push to Artifact Registry.
3.  Cloud Run deployment via Cloud Build.
4.  Cloud Scheduler setup.
5.  Pipelines configured via environment variables.
6.  Environment variables set via Cloud Build.
7.  Monitoring with Cloud Logging.
8.  Cloud Build triggers for automation.

**Environment Variables:**

* `_ENV`: Environment (stg, prod).
* `_INGRESS_ROW_LIMIT`: Product limit.
* `_INCREMENTAL_ONLY`: Incremental scraping ("yes" or "no").
* `_SPIDER_ID`: Spider identifier (e.g., C001-01).
* `GCP_PROJECT`: GCP project ID.
* `GCP_REGION`: GCP region.

**Secret Manager:**

* `CLOUD_SQL_USER`: MySQL username.
* `CLOUD_SQL_PASSWORD`: MySQL password.

**Database Details:**

* **GCS Bucket:** `rd-bckt-df-data-harvester`
* **BigQuery Table:** `reviewdale-datafabric.rd-data-harvester.product`
* **Cloud SQL Databases:** `rd-sql-datalake-stg` (stg), `rd-sql-datalake` (prod)

**Spider IDs:**

* `C001-01`: 91mobiles
* `C010-02`: Cardekho