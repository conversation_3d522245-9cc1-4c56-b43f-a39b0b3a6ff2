import dbPromise from './cacheDb.js';

async function insertMultipleProducts(reviews) {
    try {
        const db = await dbPromise;
        await db.exec('BEGIN TRANSACTION;');
        const insert_product = await db.prepare(`INSERT INTO reviews_ingestion_c001 (product_id, product_name, date, brand, model, series, variant) VALUES (?, ?, ?, ?, ?, ?, ?)`);
        for (const review of reviews) {
            await insert_product.run(review.product_id, review.product_name, review.date, review.brand, review.model, review.series, review.variant);
        }
        await insert_product.finalize();
        await db.exec('COMMIT;');
        console.log('All products have been inserted successfully.');
    } catch (err) {
        await db.exec('ROLLBACK;');
        console.error('Error inserting reviews:', err.message);
        throw err;
    }
}

async function getProducts() {
    try {
        const db = await dbPromise;
        const check_query = await db.all(`SELECT * FROM reviews_ingestion_c001 ORDER BY date DESC`);
        return check_query;
    } catch (error) {
        console.error('Error fetching products:', error.message);
        throw error;
    }
}

async function isProductInChecklist(product_id) {
    try {
        const db = await dbPromise;
        const check_query = await db.all(`SELECT * FROM reviews_ingestion_c001 where product_id='${product_id}' and checklist_02 is not NULL`);
        // product in checklist
        if (check_query.length === 0) {
            return false;
        }
        return true;
    } catch (error) {
        console.log(error);
    }
}

async function addProductInChecklist(product_id) {
    try {
        const db = await dbPromise;
        const update_query = await db.run(`
            UPDATE reviews_ingestion_c001
            SET checklist_02 = 1
            WHERE product_id = '${product_id}'`);
        return;
    } catch (error) {
        console.log(error);
    }
}

async function getProductsChecklist() {
    try {
        const db = await dbPromise;
        const check_query = await db.all(`SELECT * FROM reviews_ingestion_c001 where checklist_01=1`);
        return check_query;
    } catch (error) {
        console.log(error);
    }
}

export { insertMultipleProducts, getProducts, isProductInChecklist, addProductInChecklist, getProductsChecklist };
