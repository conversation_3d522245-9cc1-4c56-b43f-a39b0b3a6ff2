from data_pump.rd_db_config import connection_pool, get_connection_from_pool
import mysql # type: ignore


def extract():
    try:
        with get_connection_from_pool(connection_pool) as connection:
            if connection is None:
                print("Failed to get database connection.")
                return None

            with connection.cursor() as cursor:
                query = """
                    SELECT JSON_OBJECT(
                        'id', id,
                        'categories', CONCAT(category_id, ' > ', brand, ' > ', model),
                        'title', CONCAT(COALESCE(brand, ''), ' ', model, ' ', series, ' ', variant),
                        'description', CONCAT(attribute_16, ', ', attribute_11, ' storage, ', attribute_10, ' RAM, ', attribute_8, ', ', attribute_7),
                        'attributes', JSON_OBJECT(
                            'released', JSON_OBJECT('text', JSON_ARRAY(attribute_1)),
                            'status', JSON_OBJECT('text', JSON_ARRAY(attribute_2)),
                            'colors', JSON_OBJECT('text', JSON_ARRAY(attribute_3)),
                            'dimensions', JSON_OBJECT('text', JSON_ARRAY(attribute_4)),
                            'net_weight', JSON_OBJECT('text', JSON_ARRAY(attribute_5)),
                            'battery', JSON_OBJECT('text', JSON_ARRAY(attribute_7)),
                            'camera', JSON_OBJECT('text', JSON_ARRAY(attribute_8)),
                            'display_size', JSON_OBJECT('text', JSON_ARRAY(attribute_9)),
                            'ram', JSON_OBJECT('text', JSON_ARRAY(attribute_10)),
                            'storage', JSON_OBJECT('text', JSON_ARRAY(attribute_11)),
                            'os', JSON_OBJECT('text', JSON_ARRAY(attribute_12)),
                            'bluetooth', JSON_OBJECT('text', JSON_ARRAY(attribute_13)),
                            'cpu', JSON_OBJECT('text', JSON_ARRAY(attribute_14)),
                            'gpu', JSON_OBJECT('text', JSON_ARRAY(attribute_15)),
                            'display_type', JSON_OBJECT('text', JSON_ARRAY(attribute_16)),
                            'usb', JSON_OBJECT('text', JSON_ARRAY(attribute_17)),
                            'network', JSON_OBJECT('text', JSON_ARRAY(attribute_18)),
                            'charging', JSON_OBJECT('text', JSON_ARRAY(attribute_19)),
                            'fingerprint', JSON_OBJECT('text', JSON_ARRAY(attribute_20)),
                            'gps', JSON_OBJECT('text', JSON_ARRAY(attribute_21)),
                            'nfc', JSON_OBJECT('text', JSON_ARRAY(attribute_22)),
                            'wifi', JSON_OBJECT('text', JSON_ARRAY(attribute_23)),
                            'sensors', JSON_OBJECT('text', JSON_ARRAY(attribute_24))
                        ),
                        'priceInfo', JSON_OBJECT(
                            'currencyCode', 'INR',
                            'price', price
                        ),
                        'availableTime', create_date,
                        'availableQuantity', total_reviews,
                        'rating', JSON_OBJECT(
                            'totalReviews', total_reviews,
                            'productValue', value 
                        )
                    ) AS product_data
                    FROM rd_db_app.vw_product order by total_reviews DESC limit 10;
                """
                cursor.execute(query)
                result = cursor.fetch()
                return result
    except AttributeError as e:
        print(f"is_brand_id_present sublist due to AttributeError: - Error: {e}")
        
        
print(extract())
        
        