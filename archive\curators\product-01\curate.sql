select count(*) from df_ppl_product dpp where ingester_id = '02';

UPDATE df_ppl_product 
SET model = CONCAT(COALESCE(model, ''), ' ', COALESCE(series, '')) where ingester_id = '02';

select dppp.brand, dppp.model, dppp.variant FROM df_ppl_product dppp
INNER JOIN mstr_product dpp 
ON dppp.brand = dpp.brand 
AND dppp.model = dpp.model 
AND dppp.variant = dpp.variant where dppp.ingester_id = '02';

DELETE dpp 
FROM df_ppl_product dpp
INNER JOIN mstr_product mp 
ON mp.brand = dpp.brand 
AND mp.model = dpp.model 
AND mp.variant = dpp.variant
WHERE dpp.ingester_id = '02';


UPDATE df_ppl_product pp
INNER JOIN mstr_model mm ON pp.model = mm.name
SET pp.model_id = mm.id where pp.ingester_id = '02';

select * from df_ppl_product dpp where ingester_id = '02';

CREATE INDEX idx_mstr_model_name ON mstr_model(name);
CREATE INDEX idx_df_ppl_product_model ON df_ppl_product(model);
CREATE INDEX idx_mstr_model_ingester ON mstr_model(ingester_id);

INSERT INTO rd_db_app.mstr_model (id, name, create_date, brand, brand_id, ingester_id)
SELECT 
    CONCAT('M', LPAD(FLOOR(RAND() * 10000), 4, '0')) AS id,  
    TRIM(pp.model) AS name, 
    CURRENT_TIMESTAMP AS create_date, 
    TRIM(pp.brand) AS brand, 
    pp.brand_id,
    '02'
FROM (
    SELECT DISTINCT TRIM(model) AS model, brand, brand_id
    FROM df_ppl_product
    WHERE ingester_id = '02'
) pp
LEFT JOIN mstr_model mm 
    ON mm.name = pp.model  
WHERE mm.name IS NULL;


delete  from mstr_model where ingester_id is null;



SET @prev_model_id = NULL;
SET @variant_num = 0;

UPDATE df_ppl_product pp
JOIN (
    SELECT new_product_id, model_id,
           (@variant_num := IF(@prev_model_id = model_id, @variant_num + 1, 1)) AS rn,
           (@prev_model_id := model_id) AS temp
    FROM (SELECT new_product_id, model_id FROM df_ppl_product ORDER BY model_id, new_product_id) AS sorted
) t ON pp.new_product_id = t.new_product_id
SET pp.variant_id = CONCAT('V', LPAD(t.rn, 3, '0'))
WHERE pp.ingester_id = '02';  -- Only update records where ingester_id is '02'


UPDATE df_ppl_product
SET new_product_id = CONCAT(
    'P', 
    SUBSTRING(category_id, 2), 
    SUBSTRING(brand_id, 2), 
    SUBSTRING(model_id, 2), 
    SUBSTRING(variant_id, 2)
) where ingester_id = '02';


UPDATE df_ppl_product
SET attribute_5 = JSON_SET(
    attribute_5,
    '$."phy-qty-id"', 17,
    '$."value"', ROUND(
        CAST(NULLIF(JSON_UNQUOTE(JSON_EXTRACT(attribute_5, '$."value"')), '') AS DECIMAL(10,4)) / 1000,
        4
    )
)
WHERE JSON_EXTRACT(attribute_5, '$."phy-qty-id"') = 2 
AND ingester_id = '02';

UPDATE df_ppl_product
SET attribute_7 = JSON_SET(
    attribute_7,
    '$."value"', ROUND(
        CAST(NULLIF(JSON_UNQUOTE(JSON_EXTRACT(attribute_7, '$."value"')), '') AS DECIMAL(10,4)) / 1000,
        4
    )
)
WHERE ingester_id = '02';


UPDATE df_ppl_product
SET attribute_16 = JSON_SET(
    attribute_16,
    '$."value"', TRIM(REPLACE(JSON_UNQUOTE(JSON_EXTRACT(attribute_16, '$."value"')), 'Hz', ''))
)
WHERE JSON_EXTRACT(attribute_16, '$."phy-qty-id"') = 7 and ingester_id = '02';

select * from df_ppl_product dpp where new_product_id is null;

update df_ppl_product_image pi inner join df_ppl_product p on pi.product_id = p.product_id 
set pi.product_id = p.new_product_id ;

alter table df_ppl_product_image add column ingester_id varchar(10);
alter table ref_product_image add column ingester_id varchar(10);
update df_ppl_product_image set ingester_id = '02';
update ref_product_image set ingester_id = '01';

alter table df_ppl_product_price add column ingester_id varchar(10);
alter table mstr_product_price add column ingester_id varchar(10);
update mstr_product_price set ingester_id = '01';

update df_ppl_product_price pi inner join df_ppl_product p on pi.product_id = p.product_id 
set pi.product_id = p.new_product_id ;



select dpp.brand, dpp.model, dpp.variant from df_ppl_product dpp inner join mstr_product mp on dpp.brand_id = mp.brand_id and dpp.model_id =mp.model_id and
dpp.variant_id = mp.variant_id where dpp.ingester_id = '02';

DELETE dpp 
FROM df_ppl_product dpp
INNER JOIN mstr_product mp 
ON dpp.brand_id = mp.brand_id 
AND dpp.model_id = mp.model_id 
AND dpp.variant_id = mp.variant_id
WHERE dpp.ingester_id = '02';



INSERT INTO rd_db_app.mstr_product (
    id , brand_id, brand, model, external_id, attribute_8, rating_metric_1, rating_metric_2, rating_metric_3, rating_metric_4,
    rating_prime_attribute_1, rating_prime_attribute_2, create_date, available_countries, value , category_id, is_active,
    attribute_6, attribute_4, attribute_3, attribute_5, attribute_7, attribute_9, attribute_10, attribute_11, attribute_12,
    attribute_16, attribute_1, attribute_2, attribute_13, attribute_14, attribute_15, attribute_17, attribute_18, 
    attribute_19, attribute_20, attribute_21, attribute_22, attribute_23, attribute_24, series, variant, model_id, variant_id, ingester_id 
)
SELECT 
    new_product_id,
    brand_id, brand, model, external_id, attribute_8, rating_metric_1, rating_metric_2, rating_metric_3, rating_metric_4,
    rating_prime_attribute_1, rating_prime_attribute_2, create_date, available_countries, product_value, category_id, is_active,
    attribute_6, attribute_4, attribute_3, attribute_5, attribute_7, attribute_9, attribute_10, attribute_11, attribute_12,
    attribute_16, attribute_1, attribute_2, attribute_13, attribute_14, attribute_15, attribute_17, attribute_18, 
    attribute_19, attribute_20, attribute_21, attribute_22, attribute_23, attribute_24, series, variant, model_id, variant_id, ingester_id 
FROM rd_db_app.df_ppl_product where ingester_id = '02';

select * from df_ppl_product dpp where dpp.new_product_id =  'P00109568675005'  ;

select mp.id, mp.brand, mp.model, mp.variant from mstr_product mp inner join df_ppl_product dpp on mp.id = dpp.new_product_id where dpp.ingester_id = '02';


select * from mstr_product mp where ingester_id = '02';



select *, count(*) as pcount from mstr_brand mb inner join mstr_product p on mb.id = p.brand_id where mb.id not in (select brand_id from ref_brand_image rbi)  group by p.id;

SELECT mb.id AS brand_id, mb.name AS brand_name, COUNT(p.id) AS product_count
FROM rd_db_app.mstr_brand mb
LEFT JOIN rd_db_app.mstr_product p ON mb.id = p.brand_id
WHERE mb.id NOT IN (SELECT brand_id FROM rd_db_app.ref_brand_image)
GROUP BY mb.id, mb.name order by COUNT(p.id) desc;


delete from df_ppl_product 

SELECT new_product_id, brand_id, model_id, variant_id, brand, model, variant, COUNT(*) AS duplicate_count
FROM rd_db_app.df_ppl_product
where ingester_id = '02'
GROUP BY brand, model, variant, new_product_id, brand_id, model_id, variant_id
HAVING COUNT(*) > 1;

DELETE p FROM rd_db_app.df_ppl_product p
JOIN (
    SELECT brand, model, variant
    FROM rd_db_app.df_ppl_product
    WHERE ingester_id = '02'
    GROUP BY brand, model, variant
    HAVING COUNT(*) > 1
) dup ON p.brand = dup.brand AND p.model = dup.model AND p.variant = dup.variant
WHERE p.ingester_id = '02';

SELECT new_product_id, COUNT(*) AS duplicate_count
FROM rd_db_app.df_ppl_product
where ingester_id = '02'
GROUP BY new_product_id
HAVING COUNT(*) > 1;


INSERT INTO rd_db_app.mstr_product_price
(price, product_id, id, category_id, currency, country, ingester_id)
SELECT price, product_id, id, category_id, currency, country, ingester_id
FROM rd_db_app.df_ppl_product_price;

INSERT INTO rd_db_app.ref_product_image
(id, product_id, description, image_url, create_date, identifier, a_product_id, ingester_id)
SELECT id, product_id, description, image_url, create_date, identifier, a_product_id, ingester_id
FROM rd_db_app.df_ppl_product_image;


SELECT p.id, COUNT(*) AS duplicate_count
FROM (
    SELECT id FROM rd_db_app.vw_product
    UNION ALL
    SELECT id FROM rd_db_app.mstr_product where ingester_id = '02'
) p
GROUP BY p.id
HAVING COUNT(*) > 1;



INSERT IGNORE INTO rd_db_app.vw_product (
    id, price, is_active, category_id, brand_id, brand, model, series, variant, name, 
    attribute_1, attribute_2, attribute_6, attribute_8, attribute_12, attribute_13, 
    attribute_14, attribute_15, attribute_17, attribute_18, attribute_19, attribute_20, 
    attribute_21, attribute_22, attribute_23, attribute_24, rating_metric_1, rating_metric_2, 
    rating_metric_3, rating_metric_4, rating_prime_attribute_1, rating_prime_attribute_2, 
    available_countries, value, create_date, total_reviews, total_questions, total_upvotes, 
    primary_image_url, attribute_4, brand_image_url, attribute_3, attribute_5, attribute_7, 
    attribute_9, attribute_10, attribute_11, attribute_16, model_id, variant_id, 
    a_product_id, ingester_id
)
SELECT 
    p.id, 
    COALESCE(pp.price, 0) AS price, 
    p.is_active, 
    p.category_id, 
    p.brand_id, 
    p.brand, 
    p.model, 
    p.series, 
    p.variant, 
    CONCAT(COALESCE(p.model, ''), ' ', COALESCE(p.variant, '')) AS name,
    p.attribute_1, 
    p.attribute_2, 
    p.attribute_6, 
    p.attribute_8, 
    p.attribute_12, 
    p.attribute_13, 
    p.attribute_14, 
    p.attribute_15, 
    p.attribute_17, 
    p.attribute_18, 
    p.attribute_19, 
    p.attribute_20, 
    p.attribute_21, 
    p.attribute_22, 
    p.attribute_23, 
    p.attribute_24, 
    p.rating_metric_1, 
    p.rating_metric_2, 
    p.rating_metric_3, 
    p.rating_metric_4, 
    p.rating_prime_attribute_1, 
    p.rating_prime_attribute_2, 
    p.available_countries, 
    p.value, 
    p.create_date, 
    0 AS total_reviews,  
    0 AS total_questions, 
    0 AS total_upvotes,   
    (SELECT rpi.image_url FROM rd_db_app.ref_product_image rpi 
     WHERE rpi.product_id = p.id AND rpi.ingester_id = p.ingester_id 
     AND rpi.identifier = 1 LIMIT 1) AS primary_image_url,  
    p.attribute_4, 
    (SELECT rbi.url FROM rd_db_app.ref_brand_image rbi 
     WHERE rbi.brand_id = p.brand_id LIMIT 1) AS brand_image_url,  
    p.attribute_3, 
    p.attribute_5, 
    p.attribute_7, 
    p.attribute_9, 
    p.attribute_10, 
    p.attribute_11, 
    p.attribute_16, 
    p.model_id, 
    p.variant_id, 
    p.id AS a_product_id,   -- Ensure this matches the column list
    p.ingester_id
FROM rd_db_app.mstr_product p
LEFT JOIN rd_db_app.mstr_product_price pp 
    ON p.id = pp.product_id AND p.ingester_id = pp.ingester_id
WHERE p.ingester_id = '02';



   
   
select * from mstr_product mp where ingester_id = '02';
select * from mstr_model mm where ingester_id = '02';
select * from ref_product_image  mm where ingester_id = '02';
select * from mstr_product_price mpp mm where ingester_id = '02';

SELECT id, COUNT(*) 
FROM (
    SELECT DISTINCT  p.id 
    FROM rd_db_app.mstr_product p
    inner JOIN rd_db_app.mstr_product_price pp 
        ON p.id = pp.product_id AND p.ingester_id = pp.ingester_id
    inner JOIN rd_db_app.ref_product_image rpi 
        ON p.id = rpi.product_id 
        AND p.ingester_id = rpi.ingester_id 
        AND rpi.identifier = 1
    inner JOIN rd_db_app.ref_brand_image rbi 
        ON p.brand_id = rbi.brand_id
    WHERE p.ingester_id = '02'
) subquery
GROUP BY id
HAVING COUNT(*) > 1;

SELECT p.id, COUNT(*)
FROM rd_db_app.mstr_product p
INNER JOIN rd_db_app.mstr_product_price pp 
    ON p.id = pp.product_id AND p.ingester_id = pp.ingester_id
INNER JOIN rd_db_app.ref_product_image rpi 
    ON p.id = rpi.product_id 
    AND p.ingester_id = rpi.ingester_id 
    AND rpi.identifier = 1
INNER JOIN rd_db_app.ref_brand_image rbi 
    ON p.brand_id = rbi.brand_id
WHERE p.ingester_id = '02'
GROUP BY p.id
HAVING COUNT(*) > 1;

SELECT 
    p.id, 
    MIN(pp.price) AS price, 
    p.is_active, 
    p.category_id, 
    p.brand_id, 
    p.brand, 
    p.model, 
    p.series, 
    p.variant, 
    CONCAT(COALESCE(p.model, ''), ' ', COALESCE(p.variant, '')) AS name,
    p.attribute_1, 
    p.attribute_2, 
    p.attribute_6, 
    p.attribute_8, 
    p.attribute_12, 
    p.attribute_13, 
    p.attribute_14, 
    p.attribute_15, 
    p.attribute_17, 
    p.attribute_18, 
    p.attribute_19, 
    p.attribute_20, 
    p.attribute_21, 
    p.attribute_22, 
    p.attribute_23, 
    p.attribute_24, 
    p.rating_metric_1, 
    p.rating_metric_2, 
    p.rating_metric_3, 
    p.rating_metric_4, 
    p.rating_prime_attribute_1, 
    p.rating_prime_attribute_2, 
    p.available_countries, 
    p.value, 
    p.create_date, 
    0 AS total_reviews,  
    0 AS total_questions, 
    0 AS total_upvotes,   
    MAX(rpi.image_url) AS primary_image_url,  
    p.attribute_4, 
    MAX(rbi.url) AS brand_image_url, 
    p.attribute_3, 
    p.attribute_5, 
    p.attribute_7, 
    p.attribute_9, 
    p.attribute_10, 
    p.attribute_11, 
    p.attribute_16, 
    p.model_id, 
    p.variant_id, 
    MAX(rpi.a_product_id) AS a_product_id, 
    p.ingester_id
FROM rd_db_app.mstr_product p
INNER JOIN rd_db_app.mstr_product_price pp 
    ON p.id = pp.product_id AND p.ingester_id = pp.ingester_id
INNER JOIN rd_db_app.ref_product_image rpi 
    ON p.id = rpi.product_id 
    AND p.ingester_id = rpi.ingester_id 
    AND rpi.identifier = 1  
INNER JOIN rd_db_app.ref_brand_image rbi 
    ON p.brand_id = rbi.brand_id
WHERE p.ingester_id = '02'
GROUP BY p.id, p.is_active, p.category_id, p.brand_id, p.brand, p.model, p.series, p.variant, 
    p.attribute_1, p.attribute_2, p.attribute_6, p.attribute_8, p.attribute_12, p.attribute_13, 
    p.attribute_14, p.attribute_15, p.attribute_17, p.attribute_18, p.attribute_19, p.attribute_20, 
    p.attribute_21, p.attribute_22, p.attribute_23, p.attribute_24, p.rating_metric_1, 
    p.rating_metric_2, p.rating_metric_3, p.rating_metric_4, p.rating_prime_attribute_1, 
    p.rating_prime_attribute_2, p.available_countries, p.value, p.create_date, 
    p.attribute_4, p.attribute_3, p.attribute_5, p.attribute_7, p.attribute_9, p.attribute_10, 
    p.attribute_11, p.attribute_16, p.model_id, p.variant_id, p.ingester_id;

   
select * from vw_product where id ='P00107363970001'  ;
select * from mstr_product vp where id = 'P00107363970001' ;


select * from vw_product vp where ingester_id = '02';
