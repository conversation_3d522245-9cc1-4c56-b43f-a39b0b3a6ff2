import * as cheerio from 'cheerio';

export const fetchProductInfo = (html) => {
    try {
        const $ = cheerio.load(html);
        const productSpecs = {};

        let numericPrice = null;
        const priceText = $('.pg-prd-pricewrap .price').text()?.trim() || '';
        numericPrice = priceText.replace(/[^\d]/g, '') || null;
        productSpecs.price = numericPrice

        $('.sm-fullspecs-grp').each((_, group) => {
            const groupTitle = $(group).find('> .title').text().trim();
            productSpecs[groupTitle] = {};

            $(group)
                .find('table tbody tr')
                .each((_, row) => {
                    const key = $(row).find('td.title').text().trim();
                    const valueTd = $(row).find('td').eq(1);
                    let value = valueTd
                        .find('span')
                        .map((_, s) => $(s).text().replace(/\s+/g, ' ').trim())
                        .get()
                        .join(' ')
                        .replace(/\s+/g, ' ')
                        .trim();

                    if (!value) value = valueTd.text().replace(/\s+/g, ' ').trim();

                    if (key && value) {
                        productSpecs[groupTitle][key] = value;
                    }
                });
        });

        return productSpecs;
    } catch (error) {
        console.log(error);
    }
}