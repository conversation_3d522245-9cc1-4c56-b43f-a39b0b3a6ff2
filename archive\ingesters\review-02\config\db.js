import connectionPool from "../rd-build-artifacts/database.js";

const fetch = (offset) => {
    return new Promise((resolve, reject) => {
        connectionPool.connectionPool.query(`SELECT product_id, product_name from vw_product LIMIT 5 OFFSET ${offset}`, (err, results) => {
            if (err) reject(new Error(err));
            resolve(results)
        })
    })
}

const fetchAll = () => {
    return new Promise((resolve, reject) => {
        connectionPool.connectionPool.query(`SELECT product_id, product_name, attribute_1 as date, brand, model, series, variant from vw_product`, (err, results) => {
            if (err) reject(new Error(err));
            resolve(results)
        })
    })
}

export const getProducts = async (offset) => {
    try {
        const data = await fetch(offset)
        return data
    } catch (error) {
        console.log(error);
    }
}

export const getAllProducts = async () => {
    try {
        const data = await fetchAll();
        return data
    } catch (error) {
        console.log(error);
    }
}