from google.cloud import storage
import os
from datetime import datetime, timedelta
import random
import re

def upload_to_gcs(local_path, bucket_name, blob_path):
    client = storage.Client()
    bucket = client.bucket(bucket_name)
    blob = bucket.blob(blob_path)
    blob.upload_from_filename(local_path)
    print(f"Uploaded {local_path} to gs://{bucket_name}/{blob_path}")

def parse_custom_date(date_str):
    today = datetime.today()

    def random_day(year, month):
        next_month = month % 12 + 1
        next_month_year = year if month < 12 else year + 1
        days_in_month = (datetime(next_month_year, next_month, 1) - timedelta(days=1)).day
        return random.randint(1, days_in_month)

    if date_str.strip().lower() == "today":
        return today.strftime("%Y-%m-%d")

    match = re.match(r"(\d+)\s+days?\s+ago", date_str, re.IGNORECASE)
    if match:
        days_ago = int(match.group(1))
        target_date = today - timedelta(days=days_ago)
        return target_date.strftime("%Y-%m-%d")

    match = re.match(r"(\d+)\s+months?\s+ago", date_str, re.IGNORECASE)
    if match:
        months_ago = int(match.group(1))
        month = (today.month - months_ago - 1) % 12 + 1
        year = today.year + ((today.month - months_ago - 1) // 12)
        day = random_day(year, month)
        return f"{year:04d}-{month:02d}-{day:02d}"

    match = re.match(r"([A-Za-z]{3}),\s*(\d{4})", date_str)
    if match:
        month_str, year = match.groups()
        try:
            month = datetime.strptime(month_str, "%b").month
            day = random_day(int(year), month)
            return f"{int(year):04d}-{month:02d}-{day:02d}"
        except ValueError:
            pass

    return None

def match_product_name(products, product_name):
    keywords_to_exclude = {
        "bluetooth", "cable", "case", "charger", "compatible", "cover", "earphones",
        "headphones", "phone case", "protector", "refurbished", "renewed",
        "resistance", "screen", "scratch", "speaker", "sponsored", "protective",
        "protective case", "unboxing", "unbreakable"
    }

    spec_keywords_pattern = r"\b(?:\d+gb|ram|\d+g)\b"

    cleaned_name = re.sub(spec_keywords_pattern, "", product_name, flags=re.IGNORECASE)
    cleaned_name = re.sub(r"[^\w\s]", " ", cleaned_name)  
    cleaned_name = re.sub(r"\s+", " ", cleaned_name).strip().lower()
    search_words = set(cleaned_name.split())

    matched_products = []

    for product in products:
        name = product.get('name', '')
        name_cleaned = re.sub(r"[^\w\s]", " ", name.lower())  
        name_cleaned = re.sub(r"\s+", " ", name_cleaned)

        if any(keyword in name_cleaned for keyword in keywords_to_exclude):
            continue

        if all(word in name_cleaned for word in search_words):
            matched_products.append(product)

    return matched_products