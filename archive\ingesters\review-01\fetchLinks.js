import axios from "axios";
import chalk from "chalk";

const axiosClient = axios.create({
    timeout: 840000,
});

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const headers = {
    'Content-Type': 'application/json',
    'Host': 'www.flipkart.com',
    'Origin': 'https://www.flipkart.com',
    'otracker': 'search',
    'Referer': 'https://www.flipkart.com/',
    'Sec-Ch-Ua': `"Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"`,
    'Sec-Ch-Ua-Platform': 'Windows',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'X-Forwarded-For': '127.0.0.1',
    'X-Originating-IP': '127.0.0.1',
    'X-Remote-Addr': '127.0.0.1',
    'X-Remote-IP': '127.0.0.1',
    'X-User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 FKUA/website/42/website/Desktop'
}

export const searchProduct = async (productName) => {
    try {
        const productSearchURL = `https://www.flipkart.com/search?q=${productName}&otracker=search&otracker1=search&marketplace=FLIPKART&as-show=on&as=off&Cookie=T=TI170593963393500181084674817633180454485655795973295873850620732984; _pxvid=845c0799-38fd-11ef-bc5b-4931d1a3f939; at=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjFkOTYzYzUwLTM0YjctNDA1OC1iMTNmLWY2NDhiODFjYTBkYSJ9.eyJleHAiOjE3MjQxNjQzMDcsImlhdCI6MTcyMjQzNjMwNywiaXNzIjoia2V2bGFyIiwianRpIjoiY2Q0NzQ0ZGQtNzFhNC00OTNkLWJlMTEtYzc3N2I3ZWUyOTZhIiwidHlwZSI6IkFUIiwiZElkIjoiVEkxNzA1OTM5NjMzOTM1MDAxODEwODQ2NzQ4MTc2MzMxODA0NTQ0ODU2NTU3OTU5NzMyOTU4NzM4NTA2MjA3MzI5ODQiLCJrZXZJZCI6IlZJMjlFQjgwQzVGQzJCNEQzMEJCRUM1RUNDNEU4OURDODAiLCJ0SWQiOiJtYXBpIiwidnMiOiJMTyIsInoiOiJDSCIsIm0iOnRydWUsImdlbiI6NH0.YVO6xP8yweltL-u7e53lBIfZD8gwEpVbtxHwMYjCTjU; K-ACTION=null; ud=8.icdr18KS5w96Lsef0YQs06AUSXXarlyO4ZYd7SDY3iqtssg6kNGprY2LT7DScKO76BBhKXwiJpFSEm5WihtP8kK2i8bwqqWg90dI7W1kavLCjGkDpQCNgzQAl8d6dHm5KadbwvQTIFu715rUhOu1_w; vh=633; vw=1366; dpr=1; rt=null; AMCV_17EB401053DAF4840A490D4C%40AdobeOrg=-227196251%7CMCIDTS%7C19941%7CMCMID%7C78086770538161661792125329127541616369%7CMCAAMLH-1723041108%7C12%7CMCAAMB-1723463011%7CRKhpRz8krg2tLO6pguXWp5olkAcUniQYPHaMWWgdJ3xzPWQmdj0y%7CMCOPTOUT-1722865411s%7CNONE%7CMCAID%7CNONE; qH=2951f68ff1fee1e0; s_cc=true; fonts-loaded=en_loaded; Network-Type=4g; isH2EnabledBandwidth=false; h2NetworkBandwidth=9; gpv_pn=HomePage; gpv_pn_t=FLIPKART%3AHomePage; SN=VI29EB80C5FC2B4D30BBEC5ECC4E89DC80.TOKA20EAE96ACF14EDB8452C7F3AB06F777.1722936399.LO; vd=VI29EB80C5FC2B4D30BBEC5ECC4E89DC80-1722436313102-4.1722936535.1722936386.157816733; S=d1t19Pz9EPyVZQDA/PxU/Pz8hLE0bbCdmDsNSvA/+x3/TM+/33MBLKPVUNzFqMhcJm7iSviaBVBBw2hEJHrv+JuB9jg==; s_sq=flipkart-prd%3D%2526pid%253DHomePage%2526pidt%253D1%2526oid%253DSearch%252520Icon%2526oidt%253D3%2526ot%253DSUBMIT&Host=www.flipkart.com&Referer=https://www.flipkart.com/&User-Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36`;

        await delay(5000);
        const response = await axiosClient.get(productSearchURL, {
            headers: headers
        });
        return response.data;
    } catch (error) {
        console.log("Error in Search: ", error.message);
    }
}

export const fetchDetailPageContent = async (detailPageUrl) => {
    try {
        await delay(5000);
        const response = await axiosClient.get(detailPageUrl, {
            headers: headers
        });
        return response.data;
    } catch (error) {
        console.log("Error in fetchDetailPageContent: ", error.message);
    }
}

export const fetchReviewPageContent = async (url) => {
    try {
        await delay(5000);
        const response = await axiosClient.get(url, {
            headers: headers
        });
        return response.data;
    } catch (error) {
        console.log("Error in fetchReviewPageContent: ", error.message);
    }
}