import { Storage } from '@google-cloud/storage'
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const storage = new Storage({
    projectId: 'reviewdale-datafabric',
    keyFilename: './rd-df-service-key.json'
});
const rdBucketName = 'rd-bckt-df-data-harvester';

export const uploadToRDStorageFile = async (filePath, productName) => {
    try {
        await storage.bucket(rdBucketName).upload(filePath, {
            destination: `C001/review/02/${productName}-review.json`
        });
        console.log(`${filePath} uploaded to ${rdBucketName}.`);
    } catch (error) {
        console.error('Error uploading file:', error);
    }
}

export const exportRawData = async (filePath, data) => {
    const rawData = JSON.stringify(data, null, 2);
    try {
        await fs.promises.writeFile(filePath, rawData);
        console.log("Data exported successfully: ", filePath);
    } catch (error) {
        console.error(`Error writing to ${filePath}`, error);
        throw error;
    }
} 